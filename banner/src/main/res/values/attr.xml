<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="Banner">
        <attr name="delay_time" format="integer" />
        <attr name="scroll_time" format="integer" />
        <attr name="is_auto_play" format="boolean" />
        <attr name="title_background" format="color|reference" />
        <attr name="title_textcolor" format="color" />
        <attr name="title_textsize" format="dimension" />
        <attr name="title_height" format="dimension" />
        <attr name="indicator_width" format="dimension" />
        <attr name="indicator_height" format="dimension" />
        <attr name="indicator_margin" format="dimension" />
        <attr name="indicator_drawable_selected" format="reference" />
        <attr name="indicator_drawable_unselected" format="reference" />
        <attr name="banner_layout" format="reference"/>
        <attr name="banner_default_image" format="reference"/>
        <attr name="image_scale_type" format="enum">
            <enum name="center" value="0" />
            <enum name="center_crop" value="1" />
            <enum name="center_inside" value="2" />
            <enum name="fit_center" value="3" />
            <enum name="fit_end" value="4" />
            <enum name="fit_start" value="5" />
            <enum name="fit_xy" value="6" />
            <enum name="matrix" value="7" />
        </attr>
        <!--<attr name="indicator_animator" format="reference" />-->
        <!--<attr name="indicator_animator_reverse" format="reference" />-->
    </declare-styleable>
</resources>