<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@id/bannerContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false">

    <com.youth.banner.view.BannerViewPager
        android:id="@id/bannerViewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">

        <LinearLayout
            android:id="@id/circleIndicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp"
            android:visibility="gone" />

        <TextView
            android:id="@id/numIndicator"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_alignParentRight="true"
            android:layout_margin="8dp"
            android:background="@drawable/black_background"
            android:gravity="center"
            android:text="1/1"
            android:textColor="#ffffff"
            android:visibility="gone" />

        <LinearLayout
            android:id="@id/titleView"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_below="@+id/circleIndicator"
            android:background="#44000000"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@id/bannerTitle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1000"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:gravity="center_vertical"
                android:paddingLeft="10dp"
                android:singleLine="true"
                android:textColor="#ffffff"
                android:visibility="gone" />

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <TextView
                    android:id="@id/numIndicatorInside"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:text="1/1"
                    android:textColor="#ffffff"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@id/indicatorInside"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone" />
            </FrameLayout>
        </LinearLayout>
    </RelativeLayout>


    <ImageView
        android:id="@+id/bannerDefaultImage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone"/>
</RelativeLayout>