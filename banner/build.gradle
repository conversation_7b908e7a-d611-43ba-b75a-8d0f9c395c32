apply plugin: 'com.android.library'
apply plugin: 'com.github.dcendents.android-maven'
apply plugin: 'com.jfrog.bintray'
version = "1.4.10"

android {
  compileSdkVersion 28

  defaultConfig {
    minSdkVersion 11
    targetSdkVersion 25
    versionCode 41
    versionName version
  }
  buildTypes {
    release {
      minifyEnabled false
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
  lintOptions {
    abortOnError false
  }
}

dependencies {
  implementation fileTree(dir: 'libs', include: ['*.jar'])
  compileOnly 'androidx.legacy:legacy-support-v4:1.0.0'
}


def siteUrl = 'https://github.com/youth5201314/banner'
// 项目的主页
def gitUrl = 'https://github.com/youth5201314/banner.git'
// Git仓库的url
group = "com.youth.banner" //一般填你唯一的包名
//gradlew bintrayUpload
install {
  repositories.mavenInstaller {
    // This generates POM.xml with proper parameters
    pom {
      project {
        packaging 'aar'
        // Add your description here项目描述
        name 'Android图片轮播控件'
        url siteUrl
        licenses {
          license {
            name 'The Apache Software License, Version 2.0'
            url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
          }
        }
        developers {
          developer {
            //填写的一些基本信息
            id 'youth5201314'
            name 'spring'
            email '<EMAIL>'
          }
        }
        scm {
          connection gitUrl
          developerConnection gitUrl
          url siteUrl
        }
      }
    }
  }
}
task sourcesJar(type: Jar) {
  from android.sourceSets.main.java.srcDirs
  classifier = 'sources'
}

task javadoc(type: Javadoc) {
  source = android.sourceSets.main.java.srcDirs
  classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
}

task javadocJar(type: Jar, dependsOn: javadoc) {
  classifier = 'javadoc'
  from javadoc.destinationDir
}

artifacts {
  //    archives javadocJar
  archives sourcesJar
}

Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
bintray {
  //读取Bintray帐号和密码。
  //一般的为了保密和安全性，在项目的local.properties文件中添加两行句话即可：
  //bintray.user=username
  //bintray.apikey=apikey
  user = properties.getProperty("bintray.user")
  key = properties.getProperty("bintray.apikey")
  configurations = ['archives']
  pkg {
    repo = "maven"
    name = "banner" //发布到JCenter上的项目名字
    websiteUrl = siteUrl
    vcsUrl = gitUrl
    licenses = ["Apache-2.0"]
    publish = true
  }
}


