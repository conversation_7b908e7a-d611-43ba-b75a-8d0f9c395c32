#!/bin/bash

# 检查 APK 的 16KB 页面大小兼容性
# 使用方法: ./check_16kb_compatibility.sh path/to/your.apk

if [ $# -eq 0 ]; then
    echo "使用方法: $0 <APK文件路径>"
    echo "示例: $0 app/build/outputs/apk/debug/app-debug.apk"
    exit 1
fi

APK_PATH="$1"

if [ ! -f "$APK_PATH" ]; then
    echo "错误: APK 文件不存在: $APK_PATH"
    exit 1
fi

echo "检查 APK 的 16KB 页面大小兼容性..."
echo "APK 路径: $APK_PATH"
echo ""

# 创建临时目录
TEMP_DIR=$(mktemp -d)
echo "临时目录: $TEMP_DIR"

# 解压 APK
echo "解压 APK..."
unzip -q "$APK_PATH" -d "$TEMP_DIR"

# 检查所有 .so 文件的对齐情况
echo ""
echo "检查 .so 文件的对齐情况:"
echo "================================"

INCOMPATIBLE_LIBS=()

find "$TEMP_DIR" -name "*.so" | while read -r so_file; do
    relative_path=${so_file#$TEMP_DIR/}
    echo "检查: $relative_path"
    
    # 使用 readelf 检查 LOAD 段的对齐
    if command -v readelf >/dev/null 2>&1; then
        readelf -l "$so_file" | grep "LOAD" | while read -r line; do
            # 提取对齐值 (最后一列)
            align=$(echo "$line" | awk '{print $NF}')
            if [ "$align" != "0x4000" ] && [ "$align" != "16384" ]; then
                echo "  ❌ 不兼容: 对齐值 = $align (需要 16384/0x4000)"
                echo "$relative_path" >> "$TEMP_DIR/incompatible_libs.txt"
            else
                echo "  ✅ 兼容: 对齐值 = $align"
            fi
        done
    else
        echo "  ⚠️  警告: readelf 命令不可用，无法检查对齐"
    fi
    echo ""
done

# 清理临时目录
rm -rf "$TEMP_DIR"

echo ""
echo "检查完成!"
echo ""
echo "如果发现不兼容的库，请:"
echo "1. 更新相关依赖库到最新版本"
echo "2. 联系库的维护者获取 16KB 兼容版本"
echo "3. 考虑使用替代库"
echo ""
echo "更多信息请访问: https://developer.android.com/16kb-page-size"
