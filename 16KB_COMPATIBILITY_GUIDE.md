# 16KB 页面大小兼容性指南

## 问题描述

从 2025年11月1日开始，Google Play 要求所有新应用和现有应用的更新必须支持 16KB 页面大小的 Android 15+ 设备。

当前应用遇到的错误：
```
APK app-debug.apk is not compatible with 16 KB devices. Some libraries have LOAD segments not aligned at 16 KB boundaries:
lib/arm64-v8a/libimage_processing_util_jni.so
```

## 已实施的解决方案

### 1. 更新了 Gradle 配置

**主应用 (app/build.gradle):**
- 更新 `compileSdkVersion` 到 34
- 添加 `targetSdkVersion 34`
- 添加了 16KB 支持的 packaging 选项：
```gradle
packagingOptions {
  jniLibs {
    useLegacyPackaging = false
  }
}
```

**beautifylib 模块:**
- 更新 `compileSdkVersion` 到 34
- 更新 `targetSdkVersion` 到 34
- 更新 uCrop 库从 `2.2.0-native` 到 `2.2.8`

### 2. 更新了构建工具版本

**项目级 build.gradle:**
- Android Gradle Plugin: `4.2.1` → `8.1.4`

**gradle-wrapper.properties:**
- Gradle 版本: `6.8.1` → `8.4`

### 3. 更新了依赖库

主要更新了可能包含 `libimage_processing_util_jni.so` 的 uCrop 库：
```gradle
// 旧版本 (不兼容 16KB)
implementation 'com.yalantis:ucrop:2.2.0-native'

// 新版本 (支持 16KB)
implementation 'com.yalantis:ucrop:2.2.8'
```

## 验证步骤

### 1. 清理并重新构建项目

```bash
./gradlew clean
./gradlew assembleDebug
```

### 2. 使用提供的脚本检查兼容性

```bash
./scripts/check_16kb_compatibility.sh app/build/outputs/apk/debug/app-debug.apk
```

### 3. 使用 Android Studio 的 APK Analyzer

1. 在 Android Studio 中打开 APK Analyzer
2. 选择生成的 APK 文件
3. 检查 `lib/` 目录下的 `.so` 文件
4. 确认所有库都正确对齐到 16KB 边界

## 可能需要的额外步骤

如果问题仍然存在，可能需要：

### 1. 检查其他依赖库

运行以下命令查看所有原生库依赖：
```bash
./gradlew app:dependencies --configuration debugRuntimeClasspath | grep -i native
```

### 2. 更新 CameraX 库

如果 CameraX 库版本过旧，也可能导致兼容性问题：
```gradle
// 在 app/build.gradle 中更新 CameraX 版本
def camerax_version = "1.3.0" // 或更新版本
```

### 3. 排除不兼容的架构

如果某些架构的库不兼容，可以临时排除：
```gradle
android {
  defaultConfig {
    ndk {
      // 只保留兼容的架构
      abiFilters 'armeabi-v7a', 'arm64-v8a'
    }
  }
}
```

## 测试建议

1. **在 16KB 设备上测试**：使用支持 16KB 页面大小的设备或模拟器测试
2. **功能测试**：特别测试图像处理、相机、裁剪等功能
3. **性能测试**：确认更新后的库不会影响应用性能

## 参考资源

- [Android 16KB 页面大小官方文档](https://developer.android.com/16kb-page-size)
- [Google Play 政策更新](https://support.google.com/googleplay/android-developer/answer/11043825)
- [uCrop 库 GitHub](https://github.com/Yalantis/uCrop)

## 故障排除

如果构建失败，可能需要：

1. **更新 Java 版本**：确保使用 Java 11 或更高版本
2. **清理 Gradle 缓存**：
   ```bash
   ./gradlew clean
   rm -rf ~/.gradle/caches/
   ```
3. **同步项目**：在 Android Studio 中点击 "Sync Project with Gradle Files"

## 联系支持

如果遇到其他问题，请：
1. 检查构建日志中的详细错误信息
2. 确认所有依赖库都已更新到最新版本
3. 考虑联系第三方库的维护者获取支持
