<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.probim.bimenew">
    <!-- 用于进行网络定位 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 用于读取手机当前的状态 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 用于申请调用A-GPS模块 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 读取外部存储设备 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 请求访问使用照相设备 -->

    <application
        android:name="com.probim.bimenew.application.BaseApp"
        android:allowBackup="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher_144_round"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <service
            android:name="com.probim.bimenew.activity.bro.MyService"
            android:enabled="true"
            android:exported="true" />

        <activity
            android:name="com.probim.bimenew.activity.SplashActivity"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity> <!-- 主界面 -->
        <activity
            android:name="com.probim.bimenew.activity.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" /> <!-- 登录界面 -->
        <activity
            android:name="com.probim.bimenew.activity.LoginActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreen" /> <!-- 解决高版本自动安装问题 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.probim.bimenew.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider> <!-- 友盟分享-微信配置 -->
        <activity
            android:name="com.probim.bimenew.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- 友盟分享-QQ配置 -->
        <activity
            android:name="com.tencent.tauth.AuthActivity"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tencent100424468" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.tencent.connect.common.AssistActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- 适配S8 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" /> <!-- 适配刘海屏 -->
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />
        <meta-data
            android:name="UMENG_APPKEY"
            android:value="5b6953eea40fa33fd7000282" />
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="official_website" />
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="a719f462fb22efc61b09a2f2cff2dbfb" />

        <activity
            android:name="com.probim.bimenew.activity.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" /> <!-- 项目详情 -->
        <activity
            android:name="com.probim.bimenew.activity.ProjectActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 模型详情 -->
        <activity
            android:name="com.probim.bimenew.activity.ProjectModelActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" /> <!-- 图纸浏览 -->
        <activity
            android:name="com.probim.bimenew.activity.DrawingsBrowseActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 视点详情 -->
        <activity
            android:name="com.probim.bimenew.activity.PointViewActivity"
            android:screenOrientation="portrait" /> <!-- 批注详情 -->
        <activity
            android:name="com.probim.bimenew.activity.NtationActivity"
            android:screenOrientation="portrait" /> <!-- 文件预览web -->
        <activity
            android:name="com.probim.bimenew.activity.DocPreviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 视图或者视点加载 -->
        <activity
            android:name="com.probim.bimenew.activity.LoadModelViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 发起问题 -->
        <!-- 问题搜索界面 -->
        <activity
            android:name="com.probim.bimenew.activity.SearchIssueActivity"
            android:screenOrientation="portrait" /> <!-- 问题详情界面 -->
        <!-- 输入问题标题,描述详情页 -->
        <activity
            android:name="com.probim.bimenew.activity.EdtTitleActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible" /> <!-- 图片预览界面 -->
        <activity
            android:name="com.probim.bimenew.activity.PhotoViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 输入问题标题,描述详情页 -->
        <activity
            android:name="com.probim.bimenew.activity.EdtIntroActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateVisible" /> <!-- 评论界面 -->
        <activity
            android:name="com.probim.bimenew.activity.TestActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.TestCommentsActivity"
            android:screenOrientation="portrait" /> <!-- 新增评论 -->
        <activity
            android:name="com.probim.bimenew.activity.NewCommentsActivity"
            android:screenOrientation="portrait" /> <!-- 通用web界面 -->
        <activity
            android:name="com.probim.bimenew.activity.BaseWebActivity"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 视点照片预览 -->
        <activity
            android:name="com.probim.bimenew.activity.PhotoViewForPointViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" /> <!-- 消息界面 -->
        <activity
            android:name="com.probim.bimenew.activity.MessageActivity"
            android:screenOrientation="portrait" /> <!-- 修改密码界面 -->
        <activity
            android:name="com.probim.bimenew.activity.ModifyPasswordActivity"
            android:screenOrientation="portrait" /> <!-- 新建问题模型图片预览 -->
        <activity
            android:name="com.probim.bimenew.activity.NewQuestionPhotoActivity"
            android:screenOrientation="portrait" /> <!-- DWG预览 -->
        <activity
            android:name="com.probim.bimenew.activity.DwgPreviewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/FullScreen" /> <!-- 检查详情 -->
        <!-- 新建检查 -->
        <!-- 录音service -->
        <!--
 <service android:name=".utils.RecordingService" />
        <service android:name=".utils.ReviewRecordingService" />
        -->
        <!-- 定位界面 -->
        <activity
            android:name="com.probim.bimenew.activity.PointPositionActivity"
            android:screenOrientation="portrait" /> <!-- 检查记录列表 -->
        <activity
            android:name="com.probim.bimenew.activity.CheckRecordListActivity"
            android:screenOrientation="portrait" /> <!-- 单位工程 -->
        <activity
            android:name="com.probim.bimenew.activity.SelectUnitProjectActivity"
            android:screenOrientation="portrait" /> <!-- 分步 -->
        <activity
            android:name="com.probim.bimenew.activity.FenBuProjectActivity"
            android:screenOrientation="portrait" /> <!-- 分项 -->
        <activity
            android:name="com.probim.bimenew.activity.FenXiangProjectActivity"
            android:screenOrientation="portrait" /> <!-- 检验批 -->
        <activity
            android:name="com.probim.bimenew.activity.JianYanPiActivity"
            android:screenOrientation="portrait" /> <!-- 新建检查选择模型列表 -->
        <activity
            android:name="com.probim.bimenew.activity.SelectModelActivity"
            android:screenOrientation="portrait" /> <!-- 选择模型确认界面 -->
        <activity
            android:name="com.probim.bimenew.activity.SelectModelViewActivity"
            android:screenOrientation="portrait" /> <!-- 新建问题修改 -->
        <!-- 新建问题选择模型 -->
        <activity
            android:name="com.probim.bimenew.activity.SelectModelForNewQuestionActivity"
            android:screenOrientation="portrait" /> <!-- 新建问题选择视点界面 -->
        <activity
            android:name="com.probim.bimenew.activity.PointViewSelectedForNewQuestionActivity"
            android:screenOrientation="portrait" /> <!-- 新建问题图纸选择 -->
        <activity
            android:name="com.probim.bimenew.activity.TwoDimensionalForNewQuestionActivity"
            android:screenOrientation="portrait" /> <!-- 图纸选点 -->
        <!-- 问题详情2 -->
        <!-- 问题图纸选择预览 -->
        <activity
            android:name="com.probim.bimenew.activity.DrawingPreviewForNewQuestionActivity"
            android:screenOrientation="portrait" /> <!-- 评论界面2 -->
        <activity
            android:name="com.probim.bimenew.activity.CommentsActivity"
            android:screenOrientation="portrait" /> <!-- 选点功能 -->
        <activity
            android:name="com.probim.bimenew.activity.SelectDrawingForNewCheckActivity"
            android:screenOrientation="portrait" /> <!-- 复检 -->
        <activity
            android:name="com.probim.bimenew.activity.ReviewCheckActivity"
            android:screenOrientation="portrait" /> <!-- 搜索检查列表 -->
        <activity
            android:name="com.probim.bimenew.activity.SearchCheckActivity"
            android:screenOrientation="portrait" /> <!--  -->
        <activity
            android:name="com.probim.bimenew.activity.ReviewCheck2Activity"
            android:screenOrientation="portrait" /> <!--  -->
        <activity
            android:name="com.probim.bimenew.activity.MarkActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.IssueDetailActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.NewIssueActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.SelectDrawingForNewCheck2Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.CheckDetailActivityBack"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.ProjectStructureActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.BIMNewIssueActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.EdtCompanyCodeActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.MainProjectActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity android:name="com.probim.bimenew.activity.TestWebActivity" />
        <activity
            android:name="com.probim.bimenew.activity.NewCheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.CheckDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.MaterialListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.AllMaterialListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.CheckActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.web.NewWindowWebViewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.AllPeopleListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.AllTaskListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.FullScreenActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|screenLayout"
            android:screenOrientation="landscape" />
        <activity
            android:name="com.probim.bimenew.activity.check.CheckActivityBack"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.check.AllPeopleMultiSelectListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:taskAffinity="com.probim.bimenew.user" />
        <activity
            android:name="com.probim.bimenew.activity.RoleUsersActivity"
            android:screenOrientation="portrait"
            android:taskAffinity="com.probim.bimenew.user" />
        <activity
            android:name="com.probim.bimenew.activity.bro.FirstAct"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.bro.SecondAct"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.SettingActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.AllSchedualPlanListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.ScheduleActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.AllSchedualTypeNewListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualTaskActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.TaskExamineActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.AllSchedualTaskListActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualPeopleActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualMecActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualSuperviseActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualSafeActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.NewSchedualMaterialActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.AllSchedualListActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.probim.bimenew.fragment.project.CheckListActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.AllSchedualTypeDetailsListActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.SchedualPreviewActivty"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.probim.bimenew.activity.schedule.modules.SchedualPhotoViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/black_style" />
        <activity
            android:name="com.probim.bimenew.activity.fullview.view.AllFullviewActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.fullview.view.AllFullviewItemActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name="com.probim.bimenew.activity.fullview.view.NewFullviewActivity"
            android:configChanges="keyboardHidden|orientation"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <!-- Since JCore2.0.0 Required SDK核心功能 -->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service
            android:name="com.probim.bimenew.myJpush.MyService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service> <!-- Required since 3.0.7 -->
        <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定的广播 -->
        <!-- 3.3.0开始所有事件将通过该类回调 -->
        <!-- 该广播需要继承 JPush 提供的 JPushMessageReceiver 类, 并如下新增一个 Intent-Filter -->
        <receiver
            android:name="com.probim.bimenew.myJpush.MyReciver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="cn.jpush.android.intent.RECEIVE_MESSAGE" />

                <category android:name="com.probim.bimenew" />
            </intent-filter>
        </receiver>

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="a719f462fb22efc61b09a2f2cff2dbfb" />

        <activity android:name="com.probim.bimenew.file.DocViewActivity" />
        <activity android:name="com.probim.bimenew.activity.camera.CameraFragment" />
        <activity android:name="com.probim.bimenew.activity.camera.VideoActivity" />
        <activity android:name="com.probim.bimenew.activity.camera.Video4DetailsActivity" />
        <activity
            android:name=".activity.business.QualityListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.business.SafeListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.schedule.modules.AllSchedualTaskListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.schedule.modules.NewTaskActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.check.AllProgressActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.schedule.modules.TaskDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.pano.LoadPanoActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".activity.fullview.view.AllPhotoActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.flow.AllFlowListActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.flow.AllFlowItemActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.flow.LoadFlowActivity"
            android:screenOrientation="landscape" />
        <activity
            android:name=".activity.flow.X5WebViewActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.schedule.LoadScheduleActivity"
            android:screenOrientation="portrait" />
    </application>

</manifest>