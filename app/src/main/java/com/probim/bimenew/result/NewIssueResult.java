package com.probim.bimenew.result;

public class NewIssueResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"Test":{"Token":"8A99A875C0D7ACEABA4F50BCB30D8EA6BE9ACB415A7EE958383B139EA6B17B50EA5A6DCD71218957E8C730F98C485FBB0766C00C68EB94DD892C650B75B854BA4D88FDEF1639960FEE29CD4A414CF160","Title":"Nmmm","JoinerIds":"b1f377cf-f577-4c6b-9961-9b4e049c2d0f,69c316dd-ea94-4534-81ee-94334e71cd75","FileIds":null,"ImageIds":"","EndDateStr":"2020-05-18","organizeId":"fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5","RealName":"贾磊","RelationIssueId":null,"ModelID":"","ImageUrl":"","ViewPointID":"","IssueTypeId":"859d55e6-eced-4705-86c6-1fa2d8b17726","AtUserIds":null}}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * Test : {"Token":"8A99A875C0D7ACEABA4F50BCB30D8EA6BE9ACB415A7EE958383B139EA6B17B50EA5A6DCD71218957E8C730F98C485FBB0766C00C68EB94DD892C650B75B854BA4D88FDEF1639960FEE29CD4A414CF160","Title":"Nmmm","JoinerIds":"b1f377cf-f577-4c6b-9961-9b4e049c2d0f,69c316dd-ea94-4534-81ee-94334e71cd75","FileIds":null,"ImageIds":"","EndDateStr":"2020-05-18","organizeId":"fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5","RealName":"贾磊","RelationIssueId":null,"ModelID":"","ImageUrl":"","ViewPointID":"","IssueTypeId":"859d55e6-eced-4705-86c6-1fa2d8b17726","AtUserIds":null}
     */

    private TestBean Test;

    public TestBean getTest() {
      return Test;
    }

    public void setTest(TestBean Test) {
      this.Test = Test;
    }

    public static class TestBean {
      /**
       * Token : 8A99A875C0D7ACEABA4F50BCB30D8EA6BE9ACB415A7EE958383B139EA6B17B50EA5A6DCD71218957E8C730F98C485FBB0766C00C68EB94DD892C650B75B854BA4D88FDEF1639960FEE29CD4A414CF160
       * Title : Nmmm
       * JoinerIds : b1f377cf-f577-4c6b-9961-9b4e049c2d0f,69c316dd-ea94-4534-81ee-94334e71cd75
       * FileIds : null
       * ImageIds :
       * EndDateStr : 2020-05-18
       * organizeId : fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5
       * RealName : 贾磊
       * RelationIssueId : null
       * ModelID :
       * ImageUrl :
       * ViewPointID :
       * IssueTypeId : 859d55e6-eced-4705-86c6-1fa2d8b17726
       * AtUserIds : null
       */

      private String Token;
      private String Title;
      private String JoinerIds;
      private Object FileIds;
      private String ImageIds;
      private String EndDateStr;
      private String organizeId;
      private String RealName;
      private Object RelationIssueId;
      private String ModelID;
      private String ImageUrl;
      private String ViewPointID;
      private String IssueTypeId;
      private Object AtUserIds;

      public String getToken() {
        return Token;
      }

      public void setToken(String Token) {
        this.Token = Token;
      }

      public String getTitle() {
        return Title;
      }

      public void setTitle(String Title) {
        this.Title = Title;
      }

      public String getJoinerIds() {
        return JoinerIds;
      }

      public void setJoinerIds(String JoinerIds) {
        this.JoinerIds = JoinerIds;
      }

      public Object getFileIds() {
        return FileIds;
      }

      public void setFileIds(Object FileIds) {
        this.FileIds = FileIds;
      }

      public String getImageIds() {
        return ImageIds;
      }

      public void setImageIds(String ImageIds) {
        this.ImageIds = ImageIds;
      }

      public String getEndDateStr() {
        return EndDateStr;
      }

      public void setEndDateStr(String EndDateStr) {
        this.EndDateStr = EndDateStr;
      }

      public String getOrganizeId() {
        return organizeId;
      }

      public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
      }

      public String getRealName() {
        return RealName;
      }

      public void setRealName(String RealName) {
        this.RealName = RealName;
      }

      public Object getRelationIssueId() {
        return RelationIssueId;
      }

      public void setRelationIssueId(Object RelationIssueId) {
        this.RelationIssueId = RelationIssueId;
      }

      public String getModelID() {
        return ModelID;
      }

      public void setModelID(String ModelID) {
        this.ModelID = ModelID;
      }

      public String getImageUrl() {
        return ImageUrl;
      }

      public void setImageUrl(String ImageUrl) {
        this.ImageUrl = ImageUrl;
      }

      public String getViewPointID() {
        return ViewPointID;
      }

      public void setViewPointID(String ViewPointID) {
        this.ViewPointID = ViewPointID;
      }

      public String getIssueTypeId() {
        return IssueTypeId;
      }

      public void setIssueTypeId(String IssueTypeId) {
        this.IssueTypeId = IssueTypeId;
      }

      public Object getAtUserIds() {
        return AtUserIds;
      }

      public void setAtUserIds(Object AtUserIds) {
        this.AtUserIds = AtUserIds;
      }
    }
  }
}
