package com.probim.bimenew.result;

public class DeleteCommentResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"Issue_TalkId":"e2cb1707-ad74-4feb-bbb6-ad72684c5f6b"}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * Issue_TalkId : e2cb1707-ad74-4feb-bbb6-ad72684c5f6b
     */

    private String Issue_TalkId;

    public String getIssue_TalkId() {
      return Issue_TalkId;
    }

    public void setIssue_TalkId(String Issue_TalkId) {
      this.Issue_TalkId = Issue_TalkId;
    }
  }
}
