package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

public class AllPeoplerResult implements Serializable {

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"bue_existspjcnt":0,"bue_totalpjcnt":0,"bo_FullName":"北京东晨工元科技发展有限公司","UserId":"69c316dd-ea94-4534-81ee-94334e71cd75","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"李家华","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"bue_existspjcnt":0,"bue_totalpjcnt":0,"bo_FullName":"北京东晨工元科技发展有限公司","UserId":"b1f377cf-f577-4c6b-9961-9b4e049c2d0f","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"权限测试一","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"bue_existspjcnt":0,"bue_totalpjcnt":0,"bo_FullName":"北京东晨工元科技发展有限公司","UserId":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"薛友松","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"***********","Telephone":null,"Email":"<EMAIL>","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"bue_existspjcnt":0,"bue_totalpjcnt":0,"bo_FullName":"北京东晨工元科技发展有限公司","UserId":"07fa3fb9-58c5-4627-a661-06c22a29a4ee","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"曹云龙","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean implements  Serializable{
    /**
     * bue_existspjcnt : 0
     * bue_totalpjcnt : 0
     * bo_FullName : 北京东晨工元科技发展有限公司
     * UserId : 69c316dd-ea94-4534-81ee-94334e71cd75
     * EnCode : null
     * Account : null
     * Password : null
     * Secretkey : null
     * RealName : 李家华
     * NickName : null
     * HeadIcon : null
     * QuickQuery : null
     * SimpleSpelling : null
     * Gender : null
     * Birthday : null
     * Mobile :
     * Telephone : null
     * Email :
     * OICQ : null
     * WeChat : null
     * MSN : null
     * ManagerId : null
     * Manager : null
     * OrganizeId : 997223d1-fe87-48df-9eea-cf01c8a57dbf
     * DepartmentId : null
     * RoleId : null
     * DutyId : null
     * DutyName : null
     * PostId : null
     * PostName : null
     * WorkGroupId : null
     * SecurityLevel : null
     * UserOnLine : null
     * OpenId : null
     * Question : null
     * AnswerQuestion : null
     * CheckOnLine : null
     * AllowStartTime : null
     * AllowEndTime : null
     * LockStartDate : null
     * LockEndDate : null
     * FirstVisit : null
     * PreviousVisit : null
     * LastVisit : null
     * LogOnCount : null
     * SortCode : null
     * DeleteMark : null
     * EnabledMark : null
     * Description : null
     * CreateDate : null
     * CreateUserId : null
     * CreateUserName : null
     * ModifyDate : null
     * ModifyUserId : null
     * ModifyUserName : null
     * UserType : null
     * DataFrom : null
     */

    private int bue_existspjcnt;
    private int bue_totalpjcnt;
    private String bo_FullName;
    private String UserId;
    private Object EnCode;
    private Object Account;
    private Object Password;
    private Object Secretkey;
    private String RealName;
    private Object NickName;
    private Object HeadIcon;
    private Object QuickQuery;
    private Object SimpleSpelling;
    private Object Gender;
    private Object Birthday;
    private String Mobile;
    private Object Telephone;
    private String Email;
    private Object OICQ;
    private Object WeChat;
    private Object MSN;
    private Object ManagerId;
    private Object Manager;
    private String OrganizeId;
    private Object DepartmentId;
    private Object RoleId;
    private Object DutyId;
    private Object DutyName;
    private Object PostId;
    private Object PostName;
    private Object WorkGroupId;
    private Object SecurityLevel;
    private Object UserOnLine;
    private Object OpenId;
    private Object Question;
    private Object AnswerQuestion;
    private Object CheckOnLine;
    private Object AllowStartTime;
    private Object AllowEndTime;
    private Object LockStartDate;
    private Object LockEndDate;
    private Object FirstVisit;
    private Object PreviousVisit;
    private Object LastVisit;
    private Object LogOnCount;
    private Object SortCode;
    private Object DeleteMark;
    private Object EnabledMark;
    private Object Description;
    private Object CreateDate;
    private Object CreateUserId;
    private Object CreateUserName;
    private Object ModifyDate;
    private Object ModifyUserId;
    private Object ModifyUserName;
    private Object UserType;
    private Object DataFrom;
    private boolean isSelected;

    public boolean isSelected() {
      return isSelected;
    }

    public void setSelected(boolean selected) {
      isSelected = selected;
    }

    public int getBue_existspjcnt() {
      return bue_existspjcnt;
    }

    public void setBue_existspjcnt(int bue_existspjcnt) {
      this.bue_existspjcnt = bue_existspjcnt;
    }

    public int getBue_totalpjcnt() {
      return bue_totalpjcnt;
    }

    public void setBue_totalpjcnt(int bue_totalpjcnt) {
      this.bue_totalpjcnt = bue_totalpjcnt;
    }

    public String getBo_FullName() {
      return bo_FullName;
    }

    public void setBo_FullName(String bo_FullName) {
      this.bo_FullName = bo_FullName;
    }

    public String getUserId() {
      return UserId;
    }

    public void setUserId(String UserId) {
      this.UserId = UserId;
    }

    public Object getEnCode() {
      return EnCode;
    }

    public void setEnCode(Object EnCode) {
      this.EnCode = EnCode;
    }

    public Object getAccount() {
      return Account;
    }

    public void setAccount(Object Account) {
      this.Account = Account;
    }

    public Object getPassword() {
      return Password;
    }

    public void setPassword(Object Password) {
      this.Password = Password;
    }

    public Object getSecretkey() {
      return Secretkey;
    }

    public void setSecretkey(Object Secretkey) {
      this.Secretkey = Secretkey;
    }

    public String getRealName() {
      return RealName;
    }

    public void setRealName(String RealName) {
      this.RealName = RealName;
    }

    public Object getNickName() {
      return NickName;
    }

    public void setNickName(Object NickName) {
      this.NickName = NickName;
    }

    public Object getHeadIcon() {
      return HeadIcon;
    }

    public void setHeadIcon(Object HeadIcon) {
      this.HeadIcon = HeadIcon;
    }

    public Object getQuickQuery() {
      return QuickQuery;
    }

    public void setQuickQuery(Object QuickQuery) {
      this.QuickQuery = QuickQuery;
    }

    public Object getSimpleSpelling() {
      return SimpleSpelling;
    }

    public void setSimpleSpelling(Object SimpleSpelling) {
      this.SimpleSpelling = SimpleSpelling;
    }

    public Object getGender() {
      return Gender;
    }

    public void setGender(Object Gender) {
      this.Gender = Gender;
    }

    public Object getBirthday() {
      return Birthday;
    }

    public void setBirthday(Object Birthday) {
      this.Birthday = Birthday;
    }

    public String getMobile() {
      return Mobile;
    }

    public void setMobile(String Mobile) {
      this.Mobile = Mobile;
    }

    public Object getTelephone() {
      return Telephone;
    }

    public void setTelephone(Object Telephone) {
      this.Telephone = Telephone;
    }

    public String getEmail() {
      return Email;
    }

    public void setEmail(String Email) {
      this.Email = Email;
    }

    public Object getOICQ() {
      return OICQ;
    }

    public void setOICQ(Object OICQ) {
      this.OICQ = OICQ;
    }

    public Object getWeChat() {
      return WeChat;
    }

    public void setWeChat(Object WeChat) {
      this.WeChat = WeChat;
    }

    public Object getMSN() {
      return MSN;
    }

    public void setMSN(Object MSN) {
      this.MSN = MSN;
    }

    public Object getManagerId() {
      return ManagerId;
    }

    public void setManagerId(Object ManagerId) {
      this.ManagerId = ManagerId;
    }

    public Object getManager() {
      return Manager;
    }

    public void setManager(Object Manager) {
      this.Manager = Manager;
    }

    public String getOrganizeId() {
      return OrganizeId;
    }

    public void setOrganizeId(String OrganizeId) {
      this.OrganizeId = OrganizeId;
    }

    public Object getDepartmentId() {
      return DepartmentId;
    }

    public void setDepartmentId(Object DepartmentId) {
      this.DepartmentId = DepartmentId;
    }

    public Object getRoleId() {
      return RoleId;
    }

    public void setRoleId(Object RoleId) {
      this.RoleId = RoleId;
    }

    public Object getDutyId() {
      return DutyId;
    }

    public void setDutyId(Object DutyId) {
      this.DutyId = DutyId;
    }

    public Object getDutyName() {
      return DutyName;
    }

    public void setDutyName(Object DutyName) {
      this.DutyName = DutyName;
    }

    public Object getPostId() {
      return PostId;
    }

    public void setPostId(Object PostId) {
      this.PostId = PostId;
    }

    public Object getPostName() {
      return PostName;
    }

    public void setPostName(Object PostName) {
      this.PostName = PostName;
    }

    public Object getWorkGroupId() {
      return WorkGroupId;
    }

    public void setWorkGroupId(Object WorkGroupId) {
      this.WorkGroupId = WorkGroupId;
    }

    public Object getSecurityLevel() {
      return SecurityLevel;
    }

    public void setSecurityLevel(Object SecurityLevel) {
      this.SecurityLevel = SecurityLevel;
    }

    public Object getUserOnLine() {
      return UserOnLine;
    }

    public void setUserOnLine(Object UserOnLine) {
      this.UserOnLine = UserOnLine;
    }

    public Object getOpenId() {
      return OpenId;
    }

    public void setOpenId(Object OpenId) {
      this.OpenId = OpenId;
    }

    public Object getQuestion() {
      return Question;
    }

    public void setQuestion(Object Question) {
      this.Question = Question;
    }

    public Object getAnswerQuestion() {
      return AnswerQuestion;
    }

    public void setAnswerQuestion(Object AnswerQuestion) {
      this.AnswerQuestion = AnswerQuestion;
    }

    public Object getCheckOnLine() {
      return CheckOnLine;
    }

    public void setCheckOnLine(Object CheckOnLine) {
      this.CheckOnLine = CheckOnLine;
    }

    public Object getAllowStartTime() {
      return AllowStartTime;
    }

    public void setAllowStartTime(Object AllowStartTime) {
      this.AllowStartTime = AllowStartTime;
    }

    public Object getAllowEndTime() {
      return AllowEndTime;
    }

    public void setAllowEndTime(Object AllowEndTime) {
      this.AllowEndTime = AllowEndTime;
    }

    public Object getLockStartDate() {
      return LockStartDate;
    }

    public void setLockStartDate(Object LockStartDate) {
      this.LockStartDate = LockStartDate;
    }

    public Object getLockEndDate() {
      return LockEndDate;
    }

    public void setLockEndDate(Object LockEndDate) {
      this.LockEndDate = LockEndDate;
    }

    public Object getFirstVisit() {
      return FirstVisit;
    }

    public void setFirstVisit(Object FirstVisit) {
      this.FirstVisit = FirstVisit;
    }

    public Object getPreviousVisit() {
      return PreviousVisit;
    }

    public void setPreviousVisit(Object PreviousVisit) {
      this.PreviousVisit = PreviousVisit;
    }

    public Object getLastVisit() {
      return LastVisit;
    }

    public void setLastVisit(Object LastVisit) {
      this.LastVisit = LastVisit;
    }

    public Object getLogOnCount() {
      return LogOnCount;
    }

    public void setLogOnCount(Object LogOnCount) {
      this.LogOnCount = LogOnCount;
    }

    public Object getSortCode() {
      return SortCode;
    }

    public void setSortCode(Object SortCode) {
      this.SortCode = SortCode;
    }

    public Object getDeleteMark() {
      return DeleteMark;
    }

    public void setDeleteMark(Object DeleteMark) {
      this.DeleteMark = DeleteMark;
    }

    public Object getEnabledMark() {
      return EnabledMark;
    }

    public void setEnabledMark(Object EnabledMark) {
      this.EnabledMark = EnabledMark;
    }

    public Object getDescription() {
      return Description;
    }

    public void setDescription(Object Description) {
      this.Description = Description;
    }

    public Object getCreateDate() {
      return CreateDate;
    }

    public void setCreateDate(Object CreateDate) {
      this.CreateDate = CreateDate;
    }

    public Object getCreateUserId() {
      return CreateUserId;
    }

    public void setCreateUserId(Object CreateUserId) {
      this.CreateUserId = CreateUserId;
    }

    public Object getCreateUserName() {
      return CreateUserName;
    }

    public void setCreateUserName(Object CreateUserName) {
      this.CreateUserName = CreateUserName;
    }

    public Object getModifyDate() {
      return ModifyDate;
    }

    public void setModifyDate(Object ModifyDate) {
      this.ModifyDate = ModifyDate;
    }

    public Object getModifyUserId() {
      return ModifyUserId;
    }

    public void setModifyUserId(Object ModifyUserId) {
      this.ModifyUserId = ModifyUserId;
    }

    public Object getModifyUserName() {
      return ModifyUserName;
    }

    public void setModifyUserName(Object ModifyUserName) {
      this.ModifyUserName = ModifyUserName;
    }

    public Object getUserType() {
      return UserType;
    }

    public void setUserType(Object UserType) {
      this.UserType = UserType;
    }

    public Object getDataFrom() {
      return DataFrom;
    }

    public void setDataFrom(Object DataFrom) {
      this.DataFrom = DataFrom;
    }
  }
}
