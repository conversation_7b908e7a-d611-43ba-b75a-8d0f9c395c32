package com.probim.bimenew.result;

import java.util.List;

public class CheckListResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"List":[{"ExamineRemark":"xq1","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"类别一","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"8b02f5a5-4db8-4d54-a8f9-6a0634eba383","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"有图片二2","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"合格","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"70608337-2cc2-4479-be1f-62704406e66f","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"有图片四4111","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"合格","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"2b876a92-701c-41e7-8750-6c7d922d4665","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"有图片一1","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"019b0621-13c6-48ce-bd1b-72ebd0d5fcc2","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"无图片三3111","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"d1819371-b622-4e26-8757-16c11bca7a5b","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"xq2","ec_name":"勿删2","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"311c0163-fe4e-4d12-b2d2-9ce2fb9d34a8","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"有图片一1111","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"f59f8f97-9212-4ab0-a2d7-4900b8ccff36","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"xq23","ec_name":"勿删2","ModelID":"","ViewpointID":"{\"modelid\":\"67170069-1711-4f4c-8ee0-a715325942a1\",\"elementids\":[],\"viewpoint\":null}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"7668a2f0-324a-4c59-9e7b-2b36bb59d926","ModelName":null},{"ExamineRemark":"xq1","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"92bea88f-bbef-4898-8b0c-351d25c21f16","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"无图片三3","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"a7a1fd55-7ca4-4b87-949c-0e8e841408c8","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"xq14","ec_name":"a1433","ModelID":"","ViewpointID":"","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"48de9d3d-69a4-4f42-8c3b-30d42c290754","ModelName":null},{"ExamineRemark":"有图片四4","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"合格","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"26331856-aa17-44ca-8f91-eb3a701645ed","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"有图片二2111","ec_name":"a1433","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"合格","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"b579f7ea-7de9-4fe2-8e27-57ff8aac62b8","ModelName":"设计院主楼-终稿@20190509"},{"ExamineRemark":"xq2","ec_name":"勿删2","ModelID":"3acdef11-521a-45ee-b931-afe401a72b57","ViewpointID":"{\"modelid\":\"3acdef11-521a-45ee-b931-afe401a72b57\"}","aede_severitylevel":"一般","aedt_name":"","ExamineResult":"整改","ExaminerID":"8a0f9c1c-05d7-4968-ab7e-47c434fd78c9","RealName":"薛友松","ExamineDate":"2020-01-02T00:00:00","ifpassignoreifintime":0,"ifpassintime":0,"ExamineID":"d8fa00f4-228d-4145-b1ed-ae6ffd28f546","ModelName":"设计院主楼-终稿@20190509"}]}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean {
    private java.util.List<ListBean> List;

    public List<ListBean> getList() {
      return List;
    }

    public void setList(List<ListBean> List) {
      this.List = List;
    }

    public static class ListBean {
      /**
       * ExamineRemark : xq1
       * ec_name : a1433
       * ModelID : 3acdef11-521a-45ee-b931-afe401a72b57
       * ViewpointID : {"modelid":"3acdef11-521a-45ee-b931-afe401a72b57"}
       * aede_severitylevel : 一般
       * aedt_name : 类别一
       * ExamineResult : 整改
       * ExaminerID : 8a0f9c1c-05d7-4968-ab7e-47c434fd78c9
       * RealName : 薛友松
       * ExamineDate : 2020-01-02T00:00:00
       * ifpassignoreifintime : 0
       * ifpassintime : 0
       * ExamineID : 8b02f5a5-4db8-4d54-a8f9-6a0634eba383
       * ModelName : 设计院主楼-终稿@20190509
       */

      private String ExamineRemark;
      private String ec_name;
      private String ModelID;
      private String ViewpointID;
      private String aede_severitylevel;
      private String aedt_name;
      private String ExamineResult;
      private String ExaminerID;
      private String RealName;
      private String ExamineDate;
      private int ifpassignoreifintime;
      private int ifpassintime;
      private String ExamineID;
      private String ModelName;
      private boolean IsExamineMgr;

      public boolean isExamineMgr() {
        return IsExamineMgr;
      }

      public void setExamineMgr(boolean examineMgr) {
        IsExamineMgr = examineMgr;
      }

      public String getExamineRemark() {
        return ExamineRemark;
      }

      public void setExamineRemark(String ExamineRemark) {
        this.ExamineRemark = ExamineRemark;
      }

      public String getEc_name() {
        return ec_name;
      }

      public void setEc_name(String ec_name) {
        this.ec_name = ec_name;
      }

      public String getModelID() {
        return ModelID;
      }

      public void setModelID(String ModelID) {
        this.ModelID = ModelID;
      }

      public String getViewpointID() {
        return ViewpointID;
      }

      public void setViewpointID(String ViewpointID) {
        this.ViewpointID = ViewpointID;
      }

      public String getAede_severitylevel() {
        return aede_severitylevel;
      }

      public void setAede_severitylevel(String aede_severitylevel) {
        this.aede_severitylevel = aede_severitylevel;
      }

      public String getAedt_name() {
        return aedt_name;
      }

      public void setAedt_name(String aedt_name) {
        this.aedt_name = aedt_name;
      }

      public String getExamineResult() {
        return ExamineResult;
      }

      public void setExamineResult(String ExamineResult) {
        this.ExamineResult = ExamineResult;
      }

      public String getExaminerID() {
        return ExaminerID;
      }

      public void setExaminerID(String ExaminerID) {
        this.ExaminerID = ExaminerID;
      }

      public String getRealName() {
        return RealName;
      }

      public void setRealName(String RealName) {
        this.RealName = RealName;
      }

      public String getExamineDate() {
        return ExamineDate;
      }

      public void setExamineDate(String ExamineDate) {
        this.ExamineDate = ExamineDate;
      }

      public int getIfpassignoreifintime() {
        return ifpassignoreifintime;
      }

      public void setIfpassignoreifintime(int ifpassignoreifintime) {
        this.ifpassignoreifintime = ifpassignoreifintime;
      }

      public int getIfpassintime() {
        return ifpassintime;
      }

      public void setIfpassintime(int ifpassintime) {
        this.ifpassintime = ifpassintime;
      }

      public String getExamineID() {
        return ExamineID;
      }

      public void setExamineID(String ExamineID) {
        this.ExamineID = ExamineID;
      }

      public String getModelName() {
        return ModelName;
      }

      public void setModelName(String ModelName) {
        this.ModelName = ModelName;
      }
    }
  }
}
