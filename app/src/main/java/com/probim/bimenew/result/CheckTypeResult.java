package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class CheckTypeResult implements Serializable {

  @SerializedName("Ret")
  private Integer ret;
  @SerializedName("Msg")
  private String msg;
  @SerializedName("Data")
  private DataBean data;

  public Integer getRet() {
    return ret;
  }

  public void setRet(Integer ret) {
    this.ret = ret;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public DataBean getData() {
    return data;
  }

  public void setData(DataBean data) {
    this.data = data;
  }

  public static class DataBean {
    @SerializedName("List")
    private List<ListBean> list;

    public List<ListBean> getList() {
      return list;
    }

    public void setList(List<ListBean> list) {
      this.list = list;
    }

    public static class ListBean {
      @SerializedName("AedtGuid")
      private String aedtGuid;
      @SerializedName("AedtType")
      private String aedtType;
      @SerializedName("AedtName")
      private String aedtName;
      @SerializedName("AedtOrganizeId")
      private String aedtOrganizeId;

      public String getAedtGuid() {
        return aedtGuid;
      }

      public void setAedtGuid(String aedtGuid) {
        this.aedtGuid = aedtGuid;
      }

      public String getAedtType() {
        return aedtType;
      }

      public void setAedtType(String aedtType) {
        this.aedtType = aedtType;
      }

      public String getAedtName() {
        return aedtName;
      }

      public void setAedtName(String aedtName) {
        this.aedtName = aedtName;
      }

      public String getAedtOrganizeId() {
        return aedtOrganizeId;
      }

      public void setAedtOrganizeId(String aedtOrganizeId) {
        this.aedtOrganizeId = aedtOrganizeId;
      }
    }
  }
}
