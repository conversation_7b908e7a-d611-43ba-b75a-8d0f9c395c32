package com.probim.bimenew.result;

import java.util.List;

public class IssueUploadPhotoResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"bf_guid":"9e101d01-675f-4bb8-8341-320eec113f3e","bf_md5":"7e28657db9a778cd07b0941aee47dea5","bf_path":"/IssueImages/2020/2/25/69707cca-75b5-4e1b-acde-d5421bdf3738.jpeg","bf_filename":"IMG_20200225_142738.jpeg"},{"bf_guid":"67bc9827-e8fa-491a-821c-1377c5d9f720","bf_md5":"7e28657db9a778cd07b0941aee47dea5","bf_path":"/IssueImages/2020/2/25/d2a19276-2301-48d1-8b21-ca579f63f07b.jpeg","bf_filename":"IMG_20200225_142736.jpeg"}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * bf_guid : 9e101d01-675f-4bb8-8341-320eec113f3e
     * bf_md5 : 7e28657db9a778cd07b0941aee47dea5
     * bf_path : /IssueImages/2020/2/25/69707cca-75b5-4e1b-acde-d5421bdf3738.jpeg
     * bf_filename : IMG_20200225_142738.jpeg
     */

    private String bf_guid;
    private String bf_md5;
    private String bf_path;
    private String bf_filename;

    public String getBf_guid() {
      return bf_guid;
    }

    public void setBf_guid(String bf_guid) {
      this.bf_guid = bf_guid;
    }

    public String getBf_md5() {
      return bf_md5;
    }

    public void setBf_md5(String bf_md5) {
      this.bf_md5 = bf_md5;
    }

    public String getBf_path() {
      return bf_path;
    }

    public void setBf_path(String bf_path) {
      this.bf_path = bf_path;
    }

    public String getBf_filename() {
      return bf_filename;
    }

    public void setBf_filename(String bf_filename) {
      this.bf_filename = bf_filename;
    }
  }
}
