package com.probim.bimenew.result;

public class UploadPhotoResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"bf_guid":"d1920973-fbc7-4fbb-bd8a-38b1f1fc3f94","bf_md5":"a4bbe7753b6b64eb2a9bda87eb59e817","bf_path":"/IssueImages/2020/2/21/c2fb0ab4-b270-46c5-88c3-3903eac9b873.jpg","bf_filename":"testImg.jpg"}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * bf_guid : d1920973-fbc7-4fbb-bd8a-38b1f1fc3f94
     * bf_md5 : a4bbe7753b6b64eb2a9bda87eb59e817
     * bf_path : /IssueImages/2020/2/21/c2fb0ab4-b270-46c5-88c3-3903eac9b873.jpg
     * bf_filename : testImg.jpg
     */

    private String bf_guid;
    private String bf_md5;
    private String bf_path;
    private String bf_filename;

    public String getBf_guid() {
      return bf_guid;
    }

    public void setBf_guid(String bf_guid) {
      this.bf_guid = bf_guid;
    }

    public String getBf_md5() {
      return bf_md5;
    }

    public void setBf_md5(String bf_md5) {
      this.bf_md5 = bf_md5;
    }

    public String getBf_path() {
      return bf_path;
    }

    public void setBf_path(String bf_path) {
      this.bf_path = bf_path;
    }

    public String getBf_filename() {
      return bf_filename;
    }

    public void setBf_filename(String bf_filename) {
      this.bf_filename = bf_filename;
    }
  }
}
