package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

public class ProjectStrcutreResult implements Serializable{

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"firstElements":"","containsElements":0,"ec_guid":"0511f63f-f0a8-11e9-b43e-fa163e36b61c","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"a1433","ec_code":"001","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"[{\"modelid\":\"*************-4f4c-8ee0-a715325942a1\",\"elementids\":[]}]","containsElements":2,"ec_guid":"2fcfba01-f994-4393-8188-afd4d22e4bb7","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"勿删2","ec_code":"012","ec_curlevelnumber":0,"ec_elementids":"[{\"modelid\":\"*************-4f4c-8ee0-a715325942a1\",\"elementids\":[]}]"},{"firstElements":"[{\"modelid\":\"9a601e61-6af7-4c4e-85b1-d4a0835b2d7d\",\"elementids\":[]}]","containsElements":2,"ec_guid":"300e3637-006e-11ea-b43e-fa163e36b61c","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"331647","ec_code":"004","ec_curlevelnumber":0,"ec_elementids":"[{\"modelid\":\"9a601e61-6af7-4c4e-85b1-d4a0835b2d7d\",\"elementids\":[]}]"},{"firstElements":"","containsElements":0,"ec_guid":"30634cdc-077f-4df3-8178-3ece32a4f97e","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"a2231","ec_code":"006","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"","containsElements":0,"ec_guid":"5521632c-0068-11ea-b43e-fa163e36b61c","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"155111","ec_code":"002","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"","containsElements":0,"ec_guid":"5b261ecf-5b67-401d-91f6-65100f126060","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"文档","ec_code":"008","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"[{\"modelid\":\"fef875e1-a913-4382-8bc5-9e4b79f811e7\",\"elementids\":[\"1400307\",\"1400007\",\"1400006\",\"1400005\"]}]","containsElements":1,"ec_guid":"71d5fc86-8bf4-45fb-b8c4-d7aea52fa546","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"南京江宁G58项目土建总承包工程施工总进度计划","ec_code":"007","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"","containsElements":0,"ec_guid":"807d91d6-2a83-4556-800d-a327817d3f29","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"测试展开","ec_code":"013","ec_curlevelnumber":0,"ec_elementids":""},{"firstElements":"","containsElements":0,"ec_guid":"eeae83e2-0fe9-4423-9766-10ac9d5fd573","ec_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","ec_type":"quality","ec_name":"文档","ec_code":"009","ec_curlevelnumber":0,"ec_elementids":""}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean implements Serializable {
    /**
     * firstElements :
     * containsElements : 0
     * ec_guid : 0511f63f-f0a8-11e9-b43e-fa163e36b61c
     * ec_organizeId : 48617e7b-07f2-4748-9199-238af8f2bfc6
     * ec_type : quality
     * ec_name : a1433
     * ec_code : 001
     * ec_curlevelnumber : 0
     * ec_elementids :
     */

    private String firstElements;
    private int containsElements;
    private String ec_guid;
    private String ec_organizeId;
    private String ec_type;
    private String ec_name;
    private String ec_code;
    private int ec_curlevelnumber;
    private String ec_elementids;

    public String getFirstElements() {
      return firstElements;
    }

    public void setFirstElements(String firstElements) {
      this.firstElements = firstElements;
    }

    public int getContainsElements() {
      return containsElements;
    }

    public void setContainsElements(int containsElements) {
      this.containsElements = containsElements;
    }

    public String getEc_guid() {
      return ec_guid;
    }

    public void setEc_guid(String ec_guid) {
      this.ec_guid = ec_guid;
    }

    public String getEc_organizeId() {
      return ec_organizeId;
    }

    public void setEc_organizeId(String ec_organizeId) {
      this.ec_organizeId = ec_organizeId;
    }

    public String getEc_type() {
      return ec_type;
    }

    public void setEc_type(String ec_type) {
      this.ec_type = ec_type;
    }

    public String getEc_name() {
      return ec_name;
    }

    public void setEc_name(String ec_name) {
      this.ec_name = ec_name;
    }

    public String getEc_code() {
      return ec_code;
    }

    public void setEc_code(String ec_code) {
      this.ec_code = ec_code;
    }

    public int getEc_curlevelnumber() {
      return ec_curlevelnumber;
    }

    public void setEc_curlevelnumber(int ec_curlevelnumber) {
      this.ec_curlevelnumber = ec_curlevelnumber;
    }

    public String getEc_elementids() {
      return ec_elementids;
    }

    public void setEc_elementids(String ec_elementids) {
      this.ec_elementids = ec_elementids;
    }
  }
}
