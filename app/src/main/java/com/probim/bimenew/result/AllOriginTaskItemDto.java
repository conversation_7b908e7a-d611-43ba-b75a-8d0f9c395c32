package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class AllOriginTaskItemDto implements Serializable {

    @SerializedName("UID_")
    private String uID_;
    @SerializedName("ID_")
    private int iD_;
    @SerializedName("NAME_")
    private String nAME_;
    @SerializedName("START_")
    private String sTART_;
    @SerializedName("FINISH_")
    private String fINISH_;
    @SerializedName("DURATION_")
    private int dURATION_;
    @SerializedName("WORK_")
    private int wORK_;
    @SerializedName("PERCENTCOMPLETE_")
    private int pERCENTCOMPLETE_;
    @SerializedName("MANUAL_")
    private int mANUAL_;
    @SerializedName("WEIGHT_")
    private int wEIGHT_;
    @SerializedName("CONSTRAINTTYPE_")
    private int cONSTRAINTTYPE_;
    @SerializedName("CONSTRAINTDATE_")
    private Object cONSTRAINTDATE_;
    @SerializedName("MILESTONE_")
    private int mILESTONE_;
    @SerializedName("SUMMARY_")
    private int sUMMARY_;
    @SerializedName("CRITICAL_")
    private int cRITICAL_;
    @SerializedName("PRIORITY_")
    private Object pRIORITY_;
    @SerializedName("NOTES_")
    private Object nOTES_;
    @SerializedName("DEPARTMENT_")
    private Object dEPARTMENT_;
    @SerializedName("PRINCIPAL_")
    private Object pRINCIPAL_;
    @SerializedName("PREDECESSORLINK_")
    private String pREDECESSORLINK_;
    @SerializedName("FIXEDDATE_")
    private int fIXEDDATE_;
    @SerializedName("PARENTTASKUID_")
    private String pARENTTASKUID_;
    @SerializedName("PROJECTUID_")
    private String pROJECTUID_;
    @SerializedName("ACTUALSTART_")
    private Object aCTUALSTART_;
    @SerializedName("ACTUALFINISH_")
    private Object aCTUALFINISH_;
    @SerializedName("ACTUALDURATION_")
    private Object aCTUALDURATION_;
    @SerializedName("ASSIGNMENTS_")
    private Object aSSIGNMENTS_;
    @SerializedName("WBS_")
    private Object wBS_;
    @SerializedName("CRITICAL2_")
    private int cRITICAL2_;

    public String getUID_() {
        return uID_;
    }

    public void setUID_(String uID_) {
        this.uID_ = uID_;
    }

    public int getID_() {
        return iD_;
    }

    public void setID_(int iD_) {
        this.iD_ = iD_;
    }

    public String getNAME_() {
        return nAME_;
    }

    public void setNAME_(String nAME_) {
        this.nAME_ = nAME_;
    }

    public String getSTART_() {
        return sTART_;
    }

    public void setSTART_(String sTART_) {
        this.sTART_ = sTART_;
    }

    public String getFINISH_() {
        return fINISH_;
    }

    public void setFINISH_(String fINISH_) {
        this.fINISH_ = fINISH_;
    }

    public int getDURATION_() {
        return dURATION_;
    }

    public void setDURATION_(int dURATION_) {
        this.dURATION_ = dURATION_;
    }

    public int getWORK_() {
        return wORK_;
    }

    public void setWORK_(int wORK_) {
        this.wORK_ = wORK_;
    }

    public int getPERCENTCOMPLETE_() {
        return pERCENTCOMPLETE_;
    }

    public void setPERCENTCOMPLETE_(int pERCENTCOMPLETE_) {
        this.pERCENTCOMPLETE_ = pERCENTCOMPLETE_;
    }

    public int getMANUAL_() {
        return mANUAL_;
    }

    public void setMANUAL_(int mANUAL_) {
        this.mANUAL_ = mANUAL_;
    }

    public int getWEIGHT_() {
        return wEIGHT_;
    }

    public void setWEIGHT_(int wEIGHT_) {
        this.wEIGHT_ = wEIGHT_;
    }

    public int getCONSTRAINTTYPE_() {
        return cONSTRAINTTYPE_;
    }

    public void setCONSTRAINTTYPE_(int cONSTRAINTTYPE_) {
        this.cONSTRAINTTYPE_ = cONSTRAINTTYPE_;
    }

    public Object getCONSTRAINTDATE_() {
        return cONSTRAINTDATE_;
    }

    public void setCONSTRAINTDATE_(Object cONSTRAINTDATE_) {
        this.cONSTRAINTDATE_ = cONSTRAINTDATE_;
    }

    public int getMILESTONE_() {
        return mILESTONE_;
    }

    public void setMILESTONE_(int mILESTONE_) {
        this.mILESTONE_ = mILESTONE_;
    }

    public int getSUMMARY_() {
        return sUMMARY_;
    }

    public void setSUMMARY_(int sUMMARY_) {
        this.sUMMARY_ = sUMMARY_;
    }

    public int getCRITICAL_() {
        return cRITICAL_;
    }

    public void setCRITICAL_(int cRITICAL_) {
        this.cRITICAL_ = cRITICAL_;
    }

    public Object getPRIORITY_() {
        return pRIORITY_;
    }

    public void setPRIORITY_(Object pRIORITY_) {
        this.pRIORITY_ = pRIORITY_;
    }

    public Object getNOTES_() {
        return nOTES_;
    }

    public void setNOTES_(Object nOTES_) {
        this.nOTES_ = nOTES_;
    }

    public Object getDEPARTMENT_() {
        return dEPARTMENT_;
    }

    public void setDEPARTMENT_(Object dEPARTMENT_) {
        this.dEPARTMENT_ = dEPARTMENT_;
    }

    public Object getPRINCIPAL_() {
        return pRINCIPAL_;
    }

    public void setPRINCIPAL_(Object pRINCIPAL_) {
        this.pRINCIPAL_ = pRINCIPAL_;
    }

    public String getPREDECESSORLINK_() {
        return pREDECESSORLINK_;
    }

    public void setPREDECESSORLINK_(String pREDECESSORLINK_) {
        this.pREDECESSORLINK_ = pREDECESSORLINK_;
    }

    public int getFIXEDDATE_() {
        return fIXEDDATE_;
    }

    public void setFIXEDDATE_(int fIXEDDATE_) {
        this.fIXEDDATE_ = fIXEDDATE_;
    }

    public String getPARENTTASKUID_() {
        return pARENTTASKUID_;
    }

    public void setPARENTTASKUID_(String pARENTTASKUID_) {
        this.pARENTTASKUID_ = pARENTTASKUID_;
    }

    public String getPROJECTUID_() {
        return pROJECTUID_;
    }

    public void setPROJECTUID_(String pROJECTUID_) {
        this.pROJECTUID_ = pROJECTUID_;
    }

    public Object getACTUALSTART_() {
        return aCTUALSTART_;
    }

    public void setACTUALSTART_(Object aCTUALSTART_) {
        this.aCTUALSTART_ = aCTUALSTART_;
    }

    public Object getACTUALFINISH_() {
        return aCTUALFINISH_;
    }

    public void setACTUALFINISH_(Object aCTUALFINISH_) {
        this.aCTUALFINISH_ = aCTUALFINISH_;
    }

    public Object getACTUALDURATION_() {
        return aCTUALDURATION_;
    }

    public void setACTUALDURATION_(Object aCTUALDURATION_) {
        this.aCTUALDURATION_ = aCTUALDURATION_;
    }

    public Object getASSIGNMENTS_() {
        return aSSIGNMENTS_;
    }

    public void setASSIGNMENTS_(Object aSSIGNMENTS_) {
        this.aSSIGNMENTS_ = aSSIGNMENTS_;
    }

    public Object getWBS_() {
        return wBS_;
    }

    public void setWBS_(Object wBS_) {
        this.wBS_ = wBS_;
    }

    public int getCRITICAL2_() {
        return cRITICAL2_;
    }

    public void setCRITICAL2_(int cRITICAL2_) {
        this.cRITICAL2_ = cRITICAL2_;
    }
}
