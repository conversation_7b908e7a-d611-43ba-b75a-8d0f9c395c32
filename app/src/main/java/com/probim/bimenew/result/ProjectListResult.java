package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/4/29/14:52.
 */

public class ProjectListResult implements Serializable {


  @SerializedName("Ret")
  private Integer ret;
  @SerializedName("Msg")
  private String msg;
  @SerializedName("Data")
  private DataBean data;

  public Integer getRet() {
    return ret;
  }

  public void setRet(Integer ret) {
    this.ret = ret;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public DataBean getData() {
    return data;
  }

  public void setData(DataBean data) {
    this.data = data;
  }

  public static class DataBean implements Serializable{
    @SerializedName("total")
    private Integer total;
    @SerializedName("rows")
    private List<RowsBean> rows;

    public Integer getTotal() {
      return total;
    }

    public void setTotal(Integer total) {
      this.total = total;
    }

    public List<RowsBean> getRows() {
      return rows;
    }

    public void setRows(List<RowsBean> rows) {
      this.rows = rows;
    }

    public static class RowsBean implements Serializable{
      @SerializedName("OrganizeId")
      private String organizeId;
      @SerializedName("ProjectId")
      private String projectId;
      @SerializedName("ProjectName")
      private String projectName;
      @SerializedName("OrganizeName")
      private String organizeName;
      @SerializedName("OrganizeManager")
      private String organizeManager;
      @SerializedName("Thumbnail")
      private String thumbnail;
      @SerializedName("CreateTime")
      private String createTime;
      @SerializedName("ManagerId")
      private String managerId;
      @SerializedName("Manager")
      private String manager;
      @SerializedName("IsPublic")
      private Boolean isPublic;
      @SerializedName("Description")
      private String description;
      @SerializedName("CopyFlowStatus")
      private Integer copyFlowStatus;
      @SerializedName("ExpiryTime")
      private String expiryTime;

      public String getOrganizeId() {
        return organizeId;
      }

      public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
      }

      public String getProjectId() {
        return projectId;
      }

      public void setProjectId(String projectId) {
        this.projectId = projectId;
      }

      public String getProjectName() {
        return projectName;
      }

      public void setProjectName(String projectName) {
        this.projectName = projectName;
      }

      public String getOrganizeName() {
        return organizeName;
      }

      public void setOrganizeName(String organizeName) {
        this.organizeName = organizeName;
      }

      public String getOrganizeManager() {
        return organizeManager;
      }

      public void setOrganizeManager(String organizeManager) {
        this.organizeManager = organizeManager;
      }

      public String getThumbnail() {
        return thumbnail;
      }

      public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
      }

      public String getCreateTime() {
        return createTime;
      }

      public void setCreateTime(String createTime) {
        this.createTime = createTime;
      }

      public String getManagerId() {
        return managerId;
      }

      public void setManagerId(String managerId) {
        this.managerId = managerId;
      }

      public String getManager() {
        return manager;
      }

      public void setManager(String manager) {
        this.manager = manager;
      }

      public Boolean getIsPublic() {
        return isPublic;
      }

      public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
      }

      public String getDescription() {
        return description;
      }

      public void setDescription(String description) {
        this.description = description;
      }

      public Integer getCopyFlowStatus() {
        return copyFlowStatus;
      }

      public void setCopyFlowStatus(Integer copyFlowStatus) {
        this.copyFlowStatus = copyFlowStatus;
      }

      public String getExpiryTime() {
        return expiryTime;
      }

      public void setExpiryTime(String expiryTime) {
        this.expiryTime = expiryTime;
      }
    }
  }
}
