package com.probim.bimenew.result;

import java.util.List;

public class CheckDetailResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"Attachments":[{"ExamineAttachmentID":"55F7347A-DCAD-486E-89A9-040D44D04652","ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","AttachmentType":".png","AttachmentName":"201806131548155070.png","AttachmentUrl":"/Resource/ExamineFile/201806131548155070.png","UploadDate":"2018-06-13T15:49:06","CheckFlag":1,"IsDel":0},{"ExamineAttachmentID":"E191A488-74B9-4684-91C6-1CEC6F604140","ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","AttachmentType":".png","AttachmentName":"201806131548155151.png","AttachmentUrl":"/Resource/ExamineFile/201806131548155151.png","UploadDate":"2018-06-13T15:49:06","CheckFlag":1,"IsDel":0}],"Rectifications":[{"RealName":"","RectificationID":"bb6ba528-889d-4877-ba5c-9a861404b9af","RectificationOperatorID":"a8fa3b2d-044f-482b-bfcd-edd29881b350","RectificationOperator":"13439885404","RectificationOperateFlag":1,"RectificationCheckDate":"2018-06-13T00:00:00","RectificationCheckResult":"整改","RectificationRemark":"","IsDel":false,"ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","CreateDate":"2018-06-13T15:49:06"}],"BasicInfo":{"ifpassintime":0,"ifpassignoreifintime":0,"Status":"待整改","aede_checkeruserids":"a0dbea69-248b-4110-8e0e-b75131e2d64a","aede_severitylevel":"一般","aedt_name":"类别一","aede_checkeruserobjs":[{"UserId":"a0dbea69-248b-4110-8e0e-b75131e2d64a","EnCode":null,"Account":"hb","Password":"8855d6fe69d1f3e3b4b74933068f7eda","Secretkey":"d42d5a2bf54e4c4a","RealName":"韩兵","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":1,"Birthday":null,"Mobile":"***********","Telephone":null,"Email":"<EMAIL>","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"5b762bd9-91a1-4c92-b0f1-77bddc1aa6f2","RoleId":"","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-12-20T10:49:38","LastVisit":"2019-12-20T11:18:43","LogOnCount":276,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":null,"CreateDate":"2019-03-06T13:41:51","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":"2019-03-27T17:01:51","ModifyUserId":"System","ModifyUserName":"超级管理员","UserType":null,"DataFrom":null}],"PrincipalObjs":[{"UserId":"3de28590-e898-442a-bab6-88fa8d46c60c","EnCode":"","Account":"***********","Password":"a4f4ab36ff2ced0bdfdbd98e0e750a1a","Secretkey":"57a80f91f3e998c0","RealName":"李雨晨","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":2,"Birthday":null,"Mobile":null,"Telephone":"","Email":null,"OICQ":"","WeChat":"","MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"fb49b56c-4230-4fae-a9a2-817f14ac7f28","RoleId":"0cfffc9b-799f-48a7-8302-b57f79a5cca5","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-07-28T11:07:59","LastVisit":"2019-07-29T22:07:26","LogOnCount":36,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":"","CreateDate":"2018-09-05T16:01:42","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}],"ec_name":null,"ExamineID":"8b02f5a5-4db8-4d54-a8f9-6a0634eba383","ProjectID":null,"ModelID":null,"ViewID":null,"ViewpointID":null,"LinkType":null,"LinkID":null,"Principal":null,"PrincipalID":"3de28590-e898-442a-bab6-88fa8d46c60c","RelationMember":null,"RelationMemberID":null,"Examiner":"薛友松","ExaminerID":null,"ExamineDate":null,"ExamineRemark":null,"ExamineResult":"整改","RectificateDate":"2020-01-08T00:00:00","FloorID":null,"FloorName":null,"FlowPhaseID":null,"FlowPhase":null,"Professional":null,"Category":null,"Placement":null,"FirstCheckerID":null,"FirstCheckerName":null,"FirstCheckDate":null,"FirstCheckRemark":null,"FirstCheckResult":null,"SecondCheckerID":null,"SecondCheckerName":null,"SecondCheckDate":null,"SecondCheckRemark":null,"SecondCheckResult":null,"ConstructUnit":null,"ConstructUnitID":null,"SubConstructUnit":null,"SubConstructUnitID":null,"Subdivisional":null,"SubdivisionalID":null,"VerifyBatch":null,"VerifyBatchID":null,"Axis":null,"AxisID":null,"ConstructionTeam":null,"ConstructionTeamID":null,"IsDel":null,"ProfessionalID":null,"CategoryID":null,"CreateDate":null,"Title":null},"ModelName":null}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * Attachments : [{"ExamineAttachmentID":"55F7347A-DCAD-486E-89A9-040D44D04652","ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","AttachmentType":".png","AttachmentName":"201806131548155070.png","AttachmentUrl":"/Resource/ExamineFile/201806131548155070.png","UploadDate":"2018-06-13T15:49:06","CheckFlag":1,"IsDel":0},{"ExamineAttachmentID":"E191A488-74B9-4684-91C6-1CEC6F604140","ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","AttachmentType":".png","AttachmentName":"201806131548155151.png","AttachmentUrl":"/Resource/ExamineFile/201806131548155151.png","UploadDate":"2018-06-13T15:49:06","CheckFlag":1,"IsDel":0}]
     * Rectifications : [{"RealName":"","RectificationID":"bb6ba528-889d-4877-ba5c-9a861404b9af","RectificationOperatorID":"a8fa3b2d-044f-482b-bfcd-edd29881b350","RectificationOperator":"13439885404","RectificationOperateFlag":1,"RectificationCheckDate":"2018-06-13T00:00:00","RectificationCheckResult":"整改","RectificationRemark":"","IsDel":false,"ExamineID":"1579F82C-5090-40EE-863D-D2839D0C6FD9","CreateDate":"2018-06-13T15:49:06"}]
     * BasicInfo : {"ifpassintime":0,"ifpassignoreifintime":0,"Status":"待整改","aede_checkeruserids":"a0dbea69-248b-4110-8e0e-b75131e2d64a","aede_severitylevel":"一般","aedt_name":"类别一","aede_checkeruserobjs":[{"UserId":"a0dbea69-248b-4110-8e0e-b75131e2d64a","EnCode":null,"Account":"hb","Password":"8855d6fe69d1f3e3b4b74933068f7eda","Secretkey":"d42d5a2bf54e4c4a","RealName":"韩兵","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":1,"Birthday":null,"Mobile":"***********","Telephone":null,"Email":"<EMAIL>","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"5b762bd9-91a1-4c92-b0f1-77bddc1aa6f2","RoleId":"","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-12-20T10:49:38","LastVisit":"2019-12-20T11:18:43","LogOnCount":276,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":null,"CreateDate":"2019-03-06T13:41:51","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":"2019-03-27T17:01:51","ModifyUserId":"System","ModifyUserName":"超级管理员","UserType":null,"DataFrom":null}],"PrincipalObjs":[{"UserId":"3de28590-e898-442a-bab6-88fa8d46c60c","EnCode":"","Account":"***********","Password":"a4f4ab36ff2ced0bdfdbd98e0e750a1a","Secretkey":"57a80f91f3e998c0","RealName":"李雨晨","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":2,"Birthday":null,"Mobile":null,"Telephone":"","Email":null,"OICQ":"","WeChat":"","MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"fb49b56c-4230-4fae-a9a2-817f14ac7f28","RoleId":"0cfffc9b-799f-48a7-8302-b57f79a5cca5","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-07-28T11:07:59","LastVisit":"2019-07-29T22:07:26","LogOnCount":36,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":"","CreateDate":"2018-09-05T16:01:42","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}],"ec_name":null,"ExamineID":"8b02f5a5-4db8-4d54-a8f9-6a0634eba383","ProjectID":null,"ModelID":null,"ViewID":null,"ViewpointID":null,"LinkType":null,"LinkID":null,"Principal":null,"PrincipalID":"3de28590-e898-442a-bab6-88fa8d46c60c","RelationMember":null,"RelationMemberID":null,"Examiner":"薛友松","ExaminerID":null,"ExamineDate":null,"ExamineRemark":null,"ExamineResult":"整改","RectificateDate":"2020-01-08T00:00:00","FloorID":null,"FloorName":null,"FlowPhaseID":null,"FlowPhase":null,"Professional":null,"Category":null,"Placement":null,"FirstCheckerID":null,"FirstCheckerName":null,"FirstCheckDate":null,"FirstCheckRemark":null,"FirstCheckResult":null,"SecondCheckerID":null,"SecondCheckerName":null,"SecondCheckDate":null,"SecondCheckRemark":null,"SecondCheckResult":null,"ConstructUnit":null,"ConstructUnitID":null,"SubConstructUnit":null,"SubConstructUnitID":null,"Subdivisional":null,"SubdivisionalID":null,"VerifyBatch":null,"VerifyBatchID":null,"Axis":null,"AxisID":null,"ConstructionTeam":null,"ConstructionTeamID":null,"IsDel":null,"ProfessionalID":null,"CategoryID":null,"CreateDate":null,"Title":null}
     * ModelName : null
     */

    private BasicInfoBean BasicInfo;
    private Object ModelName;
    private List<AttachmentsBean> Attachments;
    private List<RectificationsBean> Rectifications;

    public BasicInfoBean getBasicInfo() {
      return BasicInfo;
    }

    public void setBasicInfo(BasicInfoBean BasicInfo) {
      this.BasicInfo = BasicInfo;
    }

    public Object getModelName() {
      return ModelName;
    }

    public void setModelName(Object ModelName) {
      this.ModelName = ModelName;
    }

    public List<AttachmentsBean> getAttachments() {
      return Attachments;
    }

    public void setAttachments(List<AttachmentsBean> Attachments) {
      this.Attachments = Attachments;
    }

    public List<RectificationsBean> getRectifications() {
      return Rectifications;
    }

    public void setRectifications(List<RectificationsBean> Rectifications) {
      this.Rectifications = Rectifications;
    }

    public static class BasicInfoBean {
      /**
       * ifpassintime : 0
       * ifpassignoreifintime : 0
       * Status : 待整改
       * aede_checkeruserids : a0dbea69-248b-4110-8e0e-b75131e2d64a
       * aede_severitylevel : 一般
       * aedt_name : 类别一
       * aede_checkeruserobjs : [{"UserId":"a0dbea69-248b-4110-8e0e-b75131e2d64a","EnCode":null,"Account":"hb","Password":"8855d6fe69d1f3e3b4b74933068f7eda","Secretkey":"d42d5a2bf54e4c4a","RealName":"韩兵","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":1,"Birthday":null,"Mobile":"***********","Telephone":null,"Email":"<EMAIL>","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"5b762bd9-91a1-4c92-b0f1-77bddc1aa6f2","RoleId":"","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-12-20T10:49:38","LastVisit":"2019-12-20T11:18:43","LogOnCount":276,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":null,"CreateDate":"2019-03-06T13:41:51","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":"2019-03-27T17:01:51","ModifyUserId":"System","ModifyUserName":"超级管理员","UserType":null,"DataFrom":null}]
       * PrincipalObjs : [{"UserId":"3de28590-e898-442a-bab6-88fa8d46c60c","EnCode":"","Account":"***********","Password":"a4f4ab36ff2ced0bdfdbd98e0e750a1a","Secretkey":"57a80f91f3e998c0","RealName":"李雨晨","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":2,"Birthday":null,"Mobile":null,"Telephone":"","Email":null,"OICQ":"","WeChat":"","MSN":null,"ManagerId":"","Manager":null,"OrganizeId":"997223d1-fe87-48df-9eea-cf01c8a57dbf","DepartmentId":"fb49b56c-4230-4fae-a9a2-817f14ac7f28","RoleId":"0cfffc9b-799f-48a7-8302-b57f79a5cca5","DutyId":"","DutyName":null,"PostId":"","PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":1,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":"2019-07-28T11:07:59","LastVisit":"2019-07-29T22:07:26","LogOnCount":36,"SortCode":null,"DeleteMark":0,"EnabledMark":1,"Description":"","CreateDate":"2018-09-05T16:01:42","CreateUserId":"System","CreateUserName":"超级管理员","ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}]
       * ec_name : null
       * ExamineID : 8b02f5a5-4db8-4d54-a8f9-6a0634eba383
       * ProjectID : null
       * ModelID : null
       * ViewID : null
       * ViewpointID : null
       * LinkType : null
       * LinkID : null
       * Principal : null
       * PrincipalID : 3de28590-e898-442a-bab6-88fa8d46c60c
       * RelationMember : null
       * RelationMemberID : null
       * Examiner : 薛友松
       * ExaminerID : null
       * ExamineDate : null
       * ExamineRemark : null
       * ExamineResult : 整改
       * RectificateDate : 2020-01-08T00:00:00
       * FloorID : null
       * FloorName : null
       * FlowPhaseID : null
       * FlowPhase : null
       * Professional : null
       * Category : null
       * Placement : null
       * FirstCheckerID : null
       * FirstCheckerName : null
       * FirstCheckDate : null
       * FirstCheckRemark : null
       * FirstCheckResult : null
       * SecondCheckerID : null
       * SecondCheckerName : null
       * SecondCheckDate : null
       * SecondCheckRemark : null
       * SecondCheckResult : null
       * ConstructUnit : null
       * ConstructUnitID : null
       * SubConstructUnit : null
       * SubConstructUnitID : null
       * Subdivisional : null
       * SubdivisionalID : null
       * VerifyBatch : null
       * VerifyBatchID : null
       * Axis : null
       * AxisID : null
       * ConstructionTeam : null
       * ConstructionTeamID : null
       * IsDel : null
       * ProfessionalID : null
       * CategoryID : null
       * CreateDate : null
       * Title : null
       */

      private int ifpassintime;
      private int ifpassignoreifintime;
      private String Status;
      private String aede_checkeruserids;
      private String aede_severitylevel;
      private String aedt_name;
      private String ec_name;
      private String ExamineID;
      private Object ProjectID;
      private Object ModelID;
      private Object ViewID;
      private Object ViewpointID;
      private Object LinkType;
      private Object LinkID;
      private Object Principal;
      private String PrincipalID;
      private Object RelationMember;
      private Object RelationMemberID;
      private String Examiner;
      private Object ExaminerID;
      private Object ExamineDate;
      private Object ExamineRemark;
      private String ExamineResult;
      private String RectificateDate;
      private Object FloorID;
      private Object FloorName;
      private Object FlowPhaseID;
      private Object FlowPhase;
      private Object Professional;
      private Object Category;
      private Object Placement;
      private Object FirstCheckerID;
      private Object FirstCheckerName;
      private Object FirstCheckDate;
      private Object FirstCheckRemark;
      private Object FirstCheckResult;
      private Object SecondCheckerID;
      private Object SecondCheckerName;
      private Object SecondCheckDate;
      private Object SecondCheckRemark;
      private Object SecondCheckResult;
      private Object ConstructUnit;
      private Object ConstructUnitID;
      private Object SubConstructUnit;
      private Object SubConstructUnitID;
      private Object Subdivisional;
      private Object SubdivisionalID;
      private Object VerifyBatch;
      private Object VerifyBatchID;
      private Object Axis;
      private Object AxisID;
      private Object ConstructionTeam;
      private Object ConstructionTeamID;
      private Object IsDel;
      private Object ProfessionalID;
      private Object CategoryID;
      private Object CreateDate;
      private Object Title;
      private List<AedeCheckeruserobjsBean> aede_checkeruserobjs;
      private List<PrincipalObjsBean> PrincipalObjs;

      public int getIfpassintime() {
        return ifpassintime;
      }

      public void setIfpassintime(int ifpassintime) {
        this.ifpassintime = ifpassintime;
      }

      public int getIfpassignoreifintime() {
        return ifpassignoreifintime;
      }

      public void setIfpassignoreifintime(int ifpassignoreifintime) {
        this.ifpassignoreifintime = ifpassignoreifintime;
      }

      public String getStatus() {
        return Status;
      }

      public void setStatus(String Status) {
        this.Status = Status;
      }

      public String getAede_checkeruserids() {
        return aede_checkeruserids;
      }

      public void setAede_checkeruserids(String aede_checkeruserids) {
        this.aede_checkeruserids = aede_checkeruserids;
      }

      public String getAede_severitylevel() {
        return aede_severitylevel;
      }

      public void setAede_severitylevel(String aede_severitylevel) {
        this.aede_severitylevel = aede_severitylevel;
      }

      public String getAedt_name() {
        return aedt_name;
      }

      public void setAedt_name(String aedt_name) {
        this.aedt_name = aedt_name;
      }

      public String getEc_name() {
        return ec_name;
      }

      public void setEc_name(String ec_name) {
        this.ec_name = ec_name;
      }

      public String getExamineID() {
        return ExamineID;
      }

      public void setExamineID(String ExamineID) {
        this.ExamineID = ExamineID;
      }

      public Object getProjectID() {
        return ProjectID;
      }

      public void setProjectID(Object ProjectID) {
        this.ProjectID = ProjectID;
      }

      public Object getModelID() {
        return ModelID;
      }

      public void setModelID(Object ModelID) {
        this.ModelID = ModelID;
      }

      public Object getViewID() {
        return ViewID;
      }

      public void setViewID(Object ViewID) {
        this.ViewID = ViewID;
      }

      public Object getViewpointID() {
        return ViewpointID;
      }

      public void setViewpointID(Object ViewpointID) {
        this.ViewpointID = ViewpointID;
      }

      public Object getLinkType() {
        return LinkType;
      }

      public void setLinkType(Object LinkType) {
        this.LinkType = LinkType;
      }

      public Object getLinkID() {
        return LinkID;
      }

      public void setLinkID(Object LinkID) {
        this.LinkID = LinkID;
      }

      public Object getPrincipal() {
        return Principal;
      }

      public void setPrincipal(Object Principal) {
        this.Principal = Principal;
      }

      public String getPrincipalID() {
        return PrincipalID;
      }

      public void setPrincipalID(String PrincipalID) {
        this.PrincipalID = PrincipalID;
      }

      public Object getRelationMember() {
        return RelationMember;
      }

      public void setRelationMember(Object RelationMember) {
        this.RelationMember = RelationMember;
      }

      public Object getRelationMemberID() {
        return RelationMemberID;
      }

      public void setRelationMemberID(Object RelationMemberID) {
        this.RelationMemberID = RelationMemberID;
      }

      public String getExaminer() {
        return Examiner;
      }

      public void setExaminer(String Examiner) {
        this.Examiner = Examiner;
      }

      public Object getExaminerID() {
        return ExaminerID;
      }

      public void setExaminerID(Object ExaminerID) {
        this.ExaminerID = ExaminerID;
      }

      public Object getExamineDate() {
        return ExamineDate;
      }

      public void setExamineDate(Object ExamineDate) {
        this.ExamineDate = ExamineDate;
      }

      public Object getExamineRemark() {
        return ExamineRemark;
      }

      public void setExamineRemark(Object ExamineRemark) {
        this.ExamineRemark = ExamineRemark;
      }

      public String getExamineResult() {
        return ExamineResult;
      }

      public void setExamineResult(String ExamineResult) {
        this.ExamineResult = ExamineResult;
      }

      public String getRectificateDate() {
        return RectificateDate;
      }

      public void setRectificateDate(String RectificateDate) {
        this.RectificateDate = RectificateDate;
      }

      public Object getFloorID() {
        return FloorID;
      }

      public void setFloorID(Object FloorID) {
        this.FloorID = FloorID;
      }

      public Object getFloorName() {
        return FloorName;
      }

      public void setFloorName(Object FloorName) {
        this.FloorName = FloorName;
      }

      public Object getFlowPhaseID() {
        return FlowPhaseID;
      }

      public void setFlowPhaseID(Object FlowPhaseID) {
        this.FlowPhaseID = FlowPhaseID;
      }

      public Object getFlowPhase() {
        return FlowPhase;
      }

      public void setFlowPhase(Object FlowPhase) {
        this.FlowPhase = FlowPhase;
      }

      public Object getProfessional() {
        return Professional;
      }

      public void setProfessional(Object Professional) {
        this.Professional = Professional;
      }

      public Object getCategory() {
        return Category;
      }

      public void setCategory(Object Category) {
        this.Category = Category;
      }

      public Object getPlacement() {
        return Placement;
      }

      public void setPlacement(Object Placement) {
        this.Placement = Placement;
      }

      public Object getFirstCheckerID() {
        return FirstCheckerID;
      }

      public void setFirstCheckerID(Object FirstCheckerID) {
        this.FirstCheckerID = FirstCheckerID;
      }

      public Object getFirstCheckerName() {
        return FirstCheckerName;
      }

      public void setFirstCheckerName(Object FirstCheckerName) {
        this.FirstCheckerName = FirstCheckerName;
      }

      public Object getFirstCheckDate() {
        return FirstCheckDate;
      }

      public void setFirstCheckDate(Object FirstCheckDate) {
        this.FirstCheckDate = FirstCheckDate;
      }

      public Object getFirstCheckRemark() {
        return FirstCheckRemark;
      }

      public void setFirstCheckRemark(Object FirstCheckRemark) {
        this.FirstCheckRemark = FirstCheckRemark;
      }

      public Object getFirstCheckResult() {
        return FirstCheckResult;
      }

      public void setFirstCheckResult(Object FirstCheckResult) {
        this.FirstCheckResult = FirstCheckResult;
      }

      public Object getSecondCheckerID() {
        return SecondCheckerID;
      }

      public void setSecondCheckerID(Object SecondCheckerID) {
        this.SecondCheckerID = SecondCheckerID;
      }

      public Object getSecondCheckerName() {
        return SecondCheckerName;
      }

      public void setSecondCheckerName(Object SecondCheckerName) {
        this.SecondCheckerName = SecondCheckerName;
      }

      public Object getSecondCheckDate() {
        return SecondCheckDate;
      }

      public void setSecondCheckDate(Object SecondCheckDate) {
        this.SecondCheckDate = SecondCheckDate;
      }

      public Object getSecondCheckRemark() {
        return SecondCheckRemark;
      }

      public void setSecondCheckRemark(Object SecondCheckRemark) {
        this.SecondCheckRemark = SecondCheckRemark;
      }

      public Object getSecondCheckResult() {
        return SecondCheckResult;
      }

      public void setSecondCheckResult(Object SecondCheckResult) {
        this.SecondCheckResult = SecondCheckResult;
      }

      public Object getConstructUnit() {
        return ConstructUnit;
      }

      public void setConstructUnit(Object ConstructUnit) {
        this.ConstructUnit = ConstructUnit;
      }

      public Object getConstructUnitID() {
        return ConstructUnitID;
      }

      public void setConstructUnitID(Object ConstructUnitID) {
        this.ConstructUnitID = ConstructUnitID;
      }

      public Object getSubConstructUnit() {
        return SubConstructUnit;
      }

      public void setSubConstructUnit(Object SubConstructUnit) {
        this.SubConstructUnit = SubConstructUnit;
      }

      public Object getSubConstructUnitID() {
        return SubConstructUnitID;
      }

      public void setSubConstructUnitID(Object SubConstructUnitID) {
        this.SubConstructUnitID = SubConstructUnitID;
      }

      public Object getSubdivisional() {
        return Subdivisional;
      }

      public void setSubdivisional(Object Subdivisional) {
        this.Subdivisional = Subdivisional;
      }

      public Object getSubdivisionalID() {
        return SubdivisionalID;
      }

      public void setSubdivisionalID(Object SubdivisionalID) {
        this.SubdivisionalID = SubdivisionalID;
      }

      public Object getVerifyBatch() {
        return VerifyBatch;
      }

      public void setVerifyBatch(Object VerifyBatch) {
        this.VerifyBatch = VerifyBatch;
      }

      public Object getVerifyBatchID() {
        return VerifyBatchID;
      }

      public void setVerifyBatchID(Object VerifyBatchID) {
        this.VerifyBatchID = VerifyBatchID;
      }

      public Object getAxis() {
        return Axis;
      }

      public void setAxis(Object Axis) {
        this.Axis = Axis;
      }

      public Object getAxisID() {
        return AxisID;
      }

      public void setAxisID(Object AxisID) {
        this.AxisID = AxisID;
      }

      public Object getConstructionTeam() {
        return ConstructionTeam;
      }

      public void setConstructionTeam(Object ConstructionTeam) {
        this.ConstructionTeam = ConstructionTeam;
      }

      public Object getConstructionTeamID() {
        return ConstructionTeamID;
      }

      public void setConstructionTeamID(Object ConstructionTeamID) {
        this.ConstructionTeamID = ConstructionTeamID;
      }

      public Object getIsDel() {
        return IsDel;
      }

      public void setIsDel(Object IsDel) {
        this.IsDel = IsDel;
      }

      public Object getProfessionalID() {
        return ProfessionalID;
      }

      public void setProfessionalID(Object ProfessionalID) {
        this.ProfessionalID = ProfessionalID;
      }

      public Object getCategoryID() {
        return CategoryID;
      }

      public void setCategoryID(Object CategoryID) {
        this.CategoryID = CategoryID;
      }

      public Object getCreateDate() {
        return CreateDate;
      }

      public void setCreateDate(Object CreateDate) {
        this.CreateDate = CreateDate;
      }

      public Object getTitle() {
        return Title;
      }

      public void setTitle(Object Title) {
        this.Title = Title;
      }

      public List<AedeCheckeruserobjsBean> getAede_checkeruserobjs() {
        return aede_checkeruserobjs;
      }

      public void setAede_checkeruserobjs(List<AedeCheckeruserobjsBean> aede_checkeruserobjs) {
        this.aede_checkeruserobjs = aede_checkeruserobjs;
      }

      public List<PrincipalObjsBean> getPrincipalObjs() {
        return PrincipalObjs;
      }

      public void setPrincipalObjs(List<PrincipalObjsBean> PrincipalObjs) {
        this.PrincipalObjs = PrincipalObjs;
      }

      public static class AedeCheckeruserobjsBean {
        /**
         * UserId : a0dbea69-248b-4110-8e0e-b75131e2d64a
         * EnCode : null
         * Account : hb
         * Password : 8855d6fe69d1f3e3b4b74933068f7eda
         * Secretkey : d42d5a2bf54e4c4a
         * RealName : 韩兵
         * NickName : null
         * HeadIcon : null
         * QuickQuery : null
         * SimpleSpelling : null
         * Gender : 1
         * Birthday : null
         * Mobile : ***********
         * Telephone : null
         * Email : <EMAIL>
         * OICQ : null
         * WeChat : null
         * MSN : null
         * ManagerId :
         * Manager : null
         * OrganizeId : 997223d1-fe87-48df-9eea-cf01c8a57dbf
         * DepartmentId : 5b762bd9-91a1-4c92-b0f1-77bddc1aa6f2
         * RoleId :
         * DutyId :
         * DutyName : null
         * PostId :
         * PostName : null
         * WorkGroupId : null
         * SecurityLevel : null
         * UserOnLine : 1
         * OpenId : null
         * Question : null
         * AnswerQuestion : null
         * CheckOnLine : null
         * AllowStartTime : null
         * AllowEndTime : null
         * LockStartDate : null
         * LockEndDate : null
         * FirstVisit : null
         * PreviousVisit : 2019-12-20T10:49:38
         * LastVisit : 2019-12-20T11:18:43
         * LogOnCount : 276
         * SortCode : null
         * DeleteMark : 0
         * EnabledMark : 1
         * Description : null
         * CreateDate : 2019-03-06T13:41:51
         * CreateUserId : System
         * CreateUserName : 超级管理员
         * ModifyDate : 2019-03-27T17:01:51
         * ModifyUserId : System
         * ModifyUserName : 超级管理员
         * UserType : null
         * DataFrom : null
         */

        private String UserId;
        private Object EnCode;
        private String Account;
        private String Password;
        private String Secretkey;
        private String RealName;
        private Object NickName;
        private Object HeadIcon;
        private Object QuickQuery;
        private Object SimpleSpelling;
        private int Gender;
        private Object Birthday;
        private String Mobile;
        private Object Telephone;
        private String Email;
        private Object OICQ;
        private Object WeChat;
        private Object MSN;
        private String ManagerId;
        private Object Manager;
        private String OrganizeId;
        private String DepartmentId;
        private String RoleId;
        private String DutyId;
        private Object DutyName;
        private String PostId;
        private Object PostName;
        private Object WorkGroupId;
        private Object SecurityLevel;
        private int UserOnLine;
        private Object OpenId;
        private Object Question;
        private Object AnswerQuestion;
        private Object CheckOnLine;
        private Object AllowStartTime;
        private Object AllowEndTime;
        private Object LockStartDate;
        private Object LockEndDate;
        private Object FirstVisit;
        private String PreviousVisit;
        private String LastVisit;
        private int LogOnCount;
        private Object SortCode;
        private int DeleteMark;
        private int EnabledMark;
        private Object Description;
        private String CreateDate;
        private String CreateUserId;
        private String CreateUserName;
        private String ModifyDate;
        private String ModifyUserId;
        private String ModifyUserName;
        private Object UserType;
        private Object DataFrom;

        public String getUserId() {
          return UserId;
        }

        public void setUserId(String UserId) {
          this.UserId = UserId;
        }

        public Object getEnCode() {
          return EnCode;
        }

        public void setEnCode(Object EnCode) {
          this.EnCode = EnCode;
        }

        public String getAccount() {
          return Account;
        }

        public void setAccount(String Account) {
          this.Account = Account;
        }

        public String getPassword() {
          return Password;
        }

        public void setPassword(String Password) {
          this.Password = Password;
        }

        public String getSecretkey() {
          return Secretkey;
        }

        public void setSecretkey(String Secretkey) {
          this.Secretkey = Secretkey;
        }

        public String getRealName() {
          return RealName;
        }

        public void setRealName(String RealName) {
          this.RealName = RealName;
        }

        public Object getNickName() {
          return NickName;
        }

        public void setNickName(Object NickName) {
          this.NickName = NickName;
        }

        public Object getHeadIcon() {
          return HeadIcon;
        }

        public void setHeadIcon(Object HeadIcon) {
          this.HeadIcon = HeadIcon;
        }

        public Object getQuickQuery() {
          return QuickQuery;
        }

        public void setQuickQuery(Object QuickQuery) {
          this.QuickQuery = QuickQuery;
        }

        public Object getSimpleSpelling() {
          return SimpleSpelling;
        }

        public void setSimpleSpelling(Object SimpleSpelling) {
          this.SimpleSpelling = SimpleSpelling;
        }

        public int getGender() {
          return Gender;
        }

        public void setGender(int Gender) {
          this.Gender = Gender;
        }

        public Object getBirthday() {
          return Birthday;
        }

        public void setBirthday(Object Birthday) {
          this.Birthday = Birthday;
        }

        public String getMobile() {
          return Mobile;
        }

        public void setMobile(String Mobile) {
          this.Mobile = Mobile;
        }

        public Object getTelephone() {
          return Telephone;
        }

        public void setTelephone(Object Telephone) {
          this.Telephone = Telephone;
        }

        public String getEmail() {
          return Email;
        }

        public void setEmail(String Email) {
          this.Email = Email;
        }

        public Object getOICQ() {
          return OICQ;
        }

        public void setOICQ(Object OICQ) {
          this.OICQ = OICQ;
        }

        public Object getWeChat() {
          return WeChat;
        }

        public void setWeChat(Object WeChat) {
          this.WeChat = WeChat;
        }

        public Object getMSN() {
          return MSN;
        }

        public void setMSN(Object MSN) {
          this.MSN = MSN;
        }

        public String getManagerId() {
          return ManagerId;
        }

        public void setManagerId(String ManagerId) {
          this.ManagerId = ManagerId;
        }

        public Object getManager() {
          return Manager;
        }

        public void setManager(Object Manager) {
          this.Manager = Manager;
        }

        public String getOrganizeId() {
          return OrganizeId;
        }

        public void setOrganizeId(String OrganizeId) {
          this.OrganizeId = OrganizeId;
        }

        public String getDepartmentId() {
          return DepartmentId;
        }

        public void setDepartmentId(String DepartmentId) {
          this.DepartmentId = DepartmentId;
        }

        public String getRoleId() {
          return RoleId;
        }

        public void setRoleId(String RoleId) {
          this.RoleId = RoleId;
        }

        public String getDutyId() {
          return DutyId;
        }

        public void setDutyId(String DutyId) {
          this.DutyId = DutyId;
        }

        public Object getDutyName() {
          return DutyName;
        }

        public void setDutyName(Object DutyName) {
          this.DutyName = DutyName;
        }

        public String getPostId() {
          return PostId;
        }

        public void setPostId(String PostId) {
          this.PostId = PostId;
        }

        public Object getPostName() {
          return PostName;
        }

        public void setPostName(Object PostName) {
          this.PostName = PostName;
        }

        public Object getWorkGroupId() {
          return WorkGroupId;
        }

        public void setWorkGroupId(Object WorkGroupId) {
          this.WorkGroupId = WorkGroupId;
        }

        public Object getSecurityLevel() {
          return SecurityLevel;
        }

        public void setSecurityLevel(Object SecurityLevel) {
          this.SecurityLevel = SecurityLevel;
        }

        public int getUserOnLine() {
          return UserOnLine;
        }

        public void setUserOnLine(int UserOnLine) {
          this.UserOnLine = UserOnLine;
        }

        public Object getOpenId() {
          return OpenId;
        }

        public void setOpenId(Object OpenId) {
          this.OpenId = OpenId;
        }

        public Object getQuestion() {
          return Question;
        }

        public void setQuestion(Object Question) {
          this.Question = Question;
        }

        public Object getAnswerQuestion() {
          return AnswerQuestion;
        }

        public void setAnswerQuestion(Object AnswerQuestion) {
          this.AnswerQuestion = AnswerQuestion;
        }

        public Object getCheckOnLine() {
          return CheckOnLine;
        }

        public void setCheckOnLine(Object CheckOnLine) {
          this.CheckOnLine = CheckOnLine;
        }

        public Object getAllowStartTime() {
          return AllowStartTime;
        }

        public void setAllowStartTime(Object AllowStartTime) {
          this.AllowStartTime = AllowStartTime;
        }

        public Object getAllowEndTime() {
          return AllowEndTime;
        }

        public void setAllowEndTime(Object AllowEndTime) {
          this.AllowEndTime = AllowEndTime;
        }

        public Object getLockStartDate() {
          return LockStartDate;
        }

        public void setLockStartDate(Object LockStartDate) {
          this.LockStartDate = LockStartDate;
        }

        public Object getLockEndDate() {
          return LockEndDate;
        }

        public void setLockEndDate(Object LockEndDate) {
          this.LockEndDate = LockEndDate;
        }

        public Object getFirstVisit() {
          return FirstVisit;
        }

        public void setFirstVisit(Object FirstVisit) {
          this.FirstVisit = FirstVisit;
        }

        public String getPreviousVisit() {
          return PreviousVisit;
        }

        public void setPreviousVisit(String PreviousVisit) {
          this.PreviousVisit = PreviousVisit;
        }

        public String getLastVisit() {
          return LastVisit;
        }

        public void setLastVisit(String LastVisit) {
          this.LastVisit = LastVisit;
        }

        public int getLogOnCount() {
          return LogOnCount;
        }

        public void setLogOnCount(int LogOnCount) {
          this.LogOnCount = LogOnCount;
        }

        public Object getSortCode() {
          return SortCode;
        }

        public void setSortCode(Object SortCode) {
          this.SortCode = SortCode;
        }

        public int getDeleteMark() {
          return DeleteMark;
        }

        public void setDeleteMark(int DeleteMark) {
          this.DeleteMark = DeleteMark;
        }

        public int getEnabledMark() {
          return EnabledMark;
        }

        public void setEnabledMark(int EnabledMark) {
          this.EnabledMark = EnabledMark;
        }

        public Object getDescription() {
          return Description;
        }

        public void setDescription(Object Description) {
          this.Description = Description;
        }

        public String getCreateDate() {
          return CreateDate;
        }

        public void setCreateDate(String CreateDate) {
          this.CreateDate = CreateDate;
        }

        public String getCreateUserId() {
          return CreateUserId;
        }

        public void setCreateUserId(String CreateUserId) {
          this.CreateUserId = CreateUserId;
        }

        public String getCreateUserName() {
          return CreateUserName;
        }

        public void setCreateUserName(String CreateUserName) {
          this.CreateUserName = CreateUserName;
        }

        public String getModifyDate() {
          return ModifyDate;
        }

        public void setModifyDate(String ModifyDate) {
          this.ModifyDate = ModifyDate;
        }

        public String getModifyUserId() {
          return ModifyUserId;
        }

        public void setModifyUserId(String ModifyUserId) {
          this.ModifyUserId = ModifyUserId;
        }

        public String getModifyUserName() {
          return ModifyUserName;
        }

        public void setModifyUserName(String ModifyUserName) {
          this.ModifyUserName = ModifyUserName;
        }

        public Object getUserType() {
          return UserType;
        }

        public void setUserType(Object UserType) {
          this.UserType = UserType;
        }

        public Object getDataFrom() {
          return DataFrom;
        }

        public void setDataFrom(Object DataFrom) {
          this.DataFrom = DataFrom;
        }
      }

      public static class PrincipalObjsBean {
        /**
         * UserId : 3de28590-e898-442a-bab6-88fa8d46c60c
         * EnCode :
         * Account : ***********
         * Password : a4f4ab36ff2ced0bdfdbd98e0e750a1a
         * Secretkey : 57a80f91f3e998c0
         * RealName : 李雨晨
         * NickName : null
         * HeadIcon : null
         * QuickQuery : null
         * SimpleSpelling : null
         * Gender : 2
         * Birthday : null
         * Mobile : null
         * Telephone :
         * Email : null
         * OICQ :
         * WeChat :
         * MSN : null
         * ManagerId :
         * Manager : null
         * OrganizeId : 997223d1-fe87-48df-9eea-cf01c8a57dbf
         * DepartmentId : fb49b56c-4230-4fae-a9a2-817f14ac7f28
         * RoleId : 0cfffc9b-799f-48a7-8302-b57f79a5cca5
         * DutyId :
         * DutyName : null
         * PostId :
         * PostName : null
         * WorkGroupId : null
         * SecurityLevel : null
         * UserOnLine : 1
         * OpenId : null
         * Question : null
         * AnswerQuestion : null
         * CheckOnLine : null
         * AllowStartTime : null
         * AllowEndTime : null
         * LockStartDate : null
         * LockEndDate : null
         * FirstVisit : null
         * PreviousVisit : 2019-07-28T11:07:59
         * LastVisit : 2019-07-29T22:07:26
         * LogOnCount : 36
         * SortCode : null
         * DeleteMark : 0
         * EnabledMark : 1
         * Description :
         * CreateDate : 2018-09-05T16:01:42
         * CreateUserId : System
         * CreateUserName : 超级管理员
         * ModifyDate : null
         * ModifyUserId : null
         * ModifyUserName : null
         * UserType : null
         * DataFrom : null
         */

        private String UserId;
        private String EnCode;
        private String Account;
        private String Password;
        private String Secretkey;
        private String RealName;
        private Object NickName;
        private Object HeadIcon;
        private Object QuickQuery;
        private Object SimpleSpelling;
        private int Gender;
        private Object Birthday;
        private Object Mobile;
        private String Telephone;
        private Object Email;
        private String OICQ;
        private String WeChat;
        private Object MSN;
        private String ManagerId;
        private Object Manager;
        private String OrganizeId;
        private String DepartmentId;
        private String RoleId;
        private String DutyId;
        private Object DutyName;
        private String PostId;
        private Object PostName;
        private Object WorkGroupId;
        private Object SecurityLevel;
        private int UserOnLine;
        private Object OpenId;
        private Object Question;
        private Object AnswerQuestion;
        private Object CheckOnLine;
        private Object AllowStartTime;
        private Object AllowEndTime;
        private Object LockStartDate;
        private Object LockEndDate;
        private Object FirstVisit;
        private String PreviousVisit;
        private String LastVisit;
        private int LogOnCount;
        private Object SortCode;
        private int DeleteMark;
        private int EnabledMark;
        private String Description;
        private String CreateDate;
        private String CreateUserId;
        private String CreateUserName;
        private Object ModifyDate;
        private Object ModifyUserId;
        private Object ModifyUserName;
        private Object UserType;
        private Object DataFrom;

        public String getUserId() {
          return UserId;
        }

        public void setUserId(String UserId) {
          this.UserId = UserId;
        }

        public String getEnCode() {
          return EnCode;
        }

        public void setEnCode(String EnCode) {
          this.EnCode = EnCode;
        }

        public String getAccount() {
          return Account;
        }

        public void setAccount(String Account) {
          this.Account = Account;
        }

        public String getPassword() {
          return Password;
        }

        public void setPassword(String Password) {
          this.Password = Password;
        }

        public String getSecretkey() {
          return Secretkey;
        }

        public void setSecretkey(String Secretkey) {
          this.Secretkey = Secretkey;
        }

        public String getRealName() {
          return RealName;
        }

        public void setRealName(String RealName) {
          this.RealName = RealName;
        }

        public Object getNickName() {
          return NickName;
        }

        public void setNickName(Object NickName) {
          this.NickName = NickName;
        }

        public Object getHeadIcon() {
          return HeadIcon;
        }

        public void setHeadIcon(Object HeadIcon) {
          this.HeadIcon = HeadIcon;
        }

        public Object getQuickQuery() {
          return QuickQuery;
        }

        public void setQuickQuery(Object QuickQuery) {
          this.QuickQuery = QuickQuery;
        }

        public Object getSimpleSpelling() {
          return SimpleSpelling;
        }

        public void setSimpleSpelling(Object SimpleSpelling) {
          this.SimpleSpelling = SimpleSpelling;
        }

        public int getGender() {
          return Gender;
        }

        public void setGender(int Gender) {
          this.Gender = Gender;
        }

        public Object getBirthday() {
          return Birthday;
        }

        public void setBirthday(Object Birthday) {
          this.Birthday = Birthday;
        }

        public Object getMobile() {
          return Mobile;
        }

        public void setMobile(Object Mobile) {
          this.Mobile = Mobile;
        }

        public String getTelephone() {
          return Telephone;
        }

        public void setTelephone(String Telephone) {
          this.Telephone = Telephone;
        }

        public Object getEmail() {
          return Email;
        }

        public void setEmail(Object Email) {
          this.Email = Email;
        }

        public String getOICQ() {
          return OICQ;
        }

        public void setOICQ(String OICQ) {
          this.OICQ = OICQ;
        }

        public String getWeChat() {
          return WeChat;
        }

        public void setWeChat(String WeChat) {
          this.WeChat = WeChat;
        }

        public Object getMSN() {
          return MSN;
        }

        public void setMSN(Object MSN) {
          this.MSN = MSN;
        }

        public String getManagerId() {
          return ManagerId;
        }

        public void setManagerId(String ManagerId) {
          this.ManagerId = ManagerId;
        }

        public Object getManager() {
          return Manager;
        }

        public void setManager(Object Manager) {
          this.Manager = Manager;
        }

        public String getOrganizeId() {
          return OrganizeId;
        }

        public void setOrganizeId(String OrganizeId) {
          this.OrganizeId = OrganizeId;
        }

        public String getDepartmentId() {
          return DepartmentId;
        }

        public void setDepartmentId(String DepartmentId) {
          this.DepartmentId = DepartmentId;
        }

        public String getRoleId() {
          return RoleId;
        }

        public void setRoleId(String RoleId) {
          this.RoleId = RoleId;
        }

        public String getDutyId() {
          return DutyId;
        }

        public void setDutyId(String DutyId) {
          this.DutyId = DutyId;
        }

        public Object getDutyName() {
          return DutyName;
        }

        public void setDutyName(Object DutyName) {
          this.DutyName = DutyName;
        }

        public String getPostId() {
          return PostId;
        }

        public void setPostId(String PostId) {
          this.PostId = PostId;
        }

        public Object getPostName() {
          return PostName;
        }

        public void setPostName(Object PostName) {
          this.PostName = PostName;
        }

        public Object getWorkGroupId() {
          return WorkGroupId;
        }

        public void setWorkGroupId(Object WorkGroupId) {
          this.WorkGroupId = WorkGroupId;
        }

        public Object getSecurityLevel() {
          return SecurityLevel;
        }

        public void setSecurityLevel(Object SecurityLevel) {
          this.SecurityLevel = SecurityLevel;
        }

        public int getUserOnLine() {
          return UserOnLine;
        }

        public void setUserOnLine(int UserOnLine) {
          this.UserOnLine = UserOnLine;
        }

        public Object getOpenId() {
          return OpenId;
        }

        public void setOpenId(Object OpenId) {
          this.OpenId = OpenId;
        }

        public Object getQuestion() {
          return Question;
        }

        public void setQuestion(Object Question) {
          this.Question = Question;
        }

        public Object getAnswerQuestion() {
          return AnswerQuestion;
        }

        public void setAnswerQuestion(Object AnswerQuestion) {
          this.AnswerQuestion = AnswerQuestion;
        }

        public Object getCheckOnLine() {
          return CheckOnLine;
        }

        public void setCheckOnLine(Object CheckOnLine) {
          this.CheckOnLine = CheckOnLine;
        }

        public Object getAllowStartTime() {
          return AllowStartTime;
        }

        public void setAllowStartTime(Object AllowStartTime) {
          this.AllowStartTime = AllowStartTime;
        }

        public Object getAllowEndTime() {
          return AllowEndTime;
        }

        public void setAllowEndTime(Object AllowEndTime) {
          this.AllowEndTime = AllowEndTime;
        }

        public Object getLockStartDate() {
          return LockStartDate;
        }

        public void setLockStartDate(Object LockStartDate) {
          this.LockStartDate = LockStartDate;
        }

        public Object getLockEndDate() {
          return LockEndDate;
        }

        public void setLockEndDate(Object LockEndDate) {
          this.LockEndDate = LockEndDate;
        }

        public Object getFirstVisit() {
          return FirstVisit;
        }

        public void setFirstVisit(Object FirstVisit) {
          this.FirstVisit = FirstVisit;
        }

        public String getPreviousVisit() {
          return PreviousVisit;
        }

        public void setPreviousVisit(String PreviousVisit) {
          this.PreviousVisit = PreviousVisit;
        }

        public String getLastVisit() {
          return LastVisit;
        }

        public void setLastVisit(String LastVisit) {
          this.LastVisit = LastVisit;
        }

        public int getLogOnCount() {
          return LogOnCount;
        }

        public void setLogOnCount(int LogOnCount) {
          this.LogOnCount = LogOnCount;
        }

        public Object getSortCode() {
          return SortCode;
        }

        public void setSortCode(Object SortCode) {
          this.SortCode = SortCode;
        }

        public int getDeleteMark() {
          return DeleteMark;
        }

        public void setDeleteMark(int DeleteMark) {
          this.DeleteMark = DeleteMark;
        }

        public int getEnabledMark() {
          return EnabledMark;
        }

        public void setEnabledMark(int EnabledMark) {
          this.EnabledMark = EnabledMark;
        }

        public String getDescription() {
          return Description;
        }

        public void setDescription(String Description) {
          this.Description = Description;
        }

        public String getCreateDate() {
          return CreateDate;
        }

        public void setCreateDate(String CreateDate) {
          this.CreateDate = CreateDate;
        }

        public String getCreateUserId() {
          return CreateUserId;
        }

        public void setCreateUserId(String CreateUserId) {
          this.CreateUserId = CreateUserId;
        }

        public String getCreateUserName() {
          return CreateUserName;
        }

        public void setCreateUserName(String CreateUserName) {
          this.CreateUserName = CreateUserName;
        }

        public Object getModifyDate() {
          return ModifyDate;
        }

        public void setModifyDate(Object ModifyDate) {
          this.ModifyDate = ModifyDate;
        }

        public Object getModifyUserId() {
          return ModifyUserId;
        }

        public void setModifyUserId(Object ModifyUserId) {
          this.ModifyUserId = ModifyUserId;
        }

        public Object getModifyUserName() {
          return ModifyUserName;
        }

        public void setModifyUserName(Object ModifyUserName) {
          this.ModifyUserName = ModifyUserName;
        }

        public Object getUserType() {
          return UserType;
        }

        public void setUserType(Object UserType) {
          this.UserType = UserType;
        }

        public Object getDataFrom() {
          return DataFrom;
        }

        public void setDataFrom(Object DataFrom) {
          this.DataFrom = DataFrom;
        }
      }
    }

    public static class AttachmentsBean {
      /**
       * ExamineAttachmentID : 55F7347A-DCAD-486E-89A9-040D44D04652
       * ExamineID : 1579F82C-5090-40EE-863D-D2839D0C6FD9
       * AttachmentType : .png
       * AttachmentName : 201806131548155070.png
       * AttachmentUrl : /Resource/ExamineFile/201806131548155070.png
       * UploadDate : 2018-06-13T15:49:06
       * CheckFlag : 1
       * IsDel : 0
       */

      private String ExamineAttachmentID;
      private String ExamineID;
      private String AttachmentType;
      private String AttachmentName;
      private String AttachmentUrl;
      private String UploadDate;
      private int CheckFlag;
      private int IsDel;

      public String getExamineAttachmentID() {
        return ExamineAttachmentID;
      }

      public void setExamineAttachmentID(String ExamineAttachmentID) {
        this.ExamineAttachmentID = ExamineAttachmentID;
      }

      public String getExamineID() {
        return ExamineID;
      }

      public void setExamineID(String ExamineID) {
        this.ExamineID = ExamineID;
      }

      public String getAttachmentType() {
        return AttachmentType;
      }

      public void setAttachmentType(String AttachmentType) {
        this.AttachmentType = AttachmentType;
      }

      public String getAttachmentName() {
        return AttachmentName;
      }

      public void setAttachmentName(String AttachmentName) {
        this.AttachmentName = AttachmentName;
      }

      public String getAttachmentUrl() {
        return AttachmentUrl;
      }

      public void setAttachmentUrl(String AttachmentUrl) {
        this.AttachmentUrl = AttachmentUrl;
      }

      public String getUploadDate() {
        return UploadDate;
      }

      public void setUploadDate(String UploadDate) {
        this.UploadDate = UploadDate;
      }

      public int getCheckFlag() {
        return CheckFlag;
      }

      public void setCheckFlag(int CheckFlag) {
        this.CheckFlag = CheckFlag;
      }

      public int getIsDel() {
        return IsDel;
      }

      public void setIsDel(int IsDel) {
        this.IsDel = IsDel;
      }
    }

    public static class RectificationsBean {
      /**
       * RealName :
       * RectificationID : bb6ba528-889d-4877-ba5c-9a861404b9af
       * RectificationOperatorID : a8fa3b2d-044f-482b-bfcd-edd29881b350
       * RectificationOperator : 13439885404
       * RectificationOperateFlag : 1
       * RectificationCheckDate : 2018-06-13T00:00:00
       * RectificationCheckResult : 整改
       * RectificationRemark :
       * IsDel : false
       * ExamineID : 1579F82C-5090-40EE-863D-D2839D0C6FD9
       * CreateDate : 2018-06-13T15:49:06
       */

      private String RealName;
      private String RectificationID;
      private String RectificationOperatorID;
      private String RectificationOperator;
      private int RectificationOperateFlag;
      private String RectificationCheckDate;
      private String RectificationCheckResult;
      private String RectificationRemark;
      private boolean IsDel;
      private String ExamineID;
      private String CreateDate;

      public String getRealName() {
        return RealName;
      }

      public void setRealName(String RealName) {
        this.RealName = RealName;
      }

      public String getRectificationID() {
        return RectificationID;
      }

      public void setRectificationID(String RectificationID) {
        this.RectificationID = RectificationID;
      }

      public String getRectificationOperatorID() {
        return RectificationOperatorID;
      }

      public void setRectificationOperatorID(String RectificationOperatorID) {
        this.RectificationOperatorID = RectificationOperatorID;
      }

      public String getRectificationOperator() {
        return RectificationOperator;
      }

      public void setRectificationOperator(String RectificationOperator) {
        this.RectificationOperator = RectificationOperator;
      }

      public int getRectificationOperateFlag() {
        return RectificationOperateFlag;
      }

      public void setRectificationOperateFlag(int RectificationOperateFlag) {
        this.RectificationOperateFlag = RectificationOperateFlag;
      }

      public String getRectificationCheckDate() {
        return RectificationCheckDate;
      }

      public void setRectificationCheckDate(String RectificationCheckDate) {
        this.RectificationCheckDate = RectificationCheckDate;
      }

      public String getRectificationCheckResult() {
        return RectificationCheckResult;
      }

      public void setRectificationCheckResult(String RectificationCheckResult) {
        this.RectificationCheckResult = RectificationCheckResult;
      }

      public String getRectificationRemark() {
        return RectificationRemark;
      }

      public void setRectificationRemark(String RectificationRemark) {
        this.RectificationRemark = RectificationRemark;
      }

      public boolean isIsDel() {
        return IsDel;
      }

      public void setIsDel(boolean IsDel) {
        this.IsDel = IsDel;
      }

      public String getExamineID() {
        return ExamineID;
      }

      public void setExamineID(String ExamineID) {
        this.ExamineID = ExamineID;
      }

      public String getCreateDate() {
        return CreateDate;
      }

      public void setCreateDate(String CreateDate) {
        this.CreateDate = CreateDate;
      }
    }
  }
}
