package com.probim.bimenew.result;

import java.util.List;

/**
 * Description :
 * Author : <PERSON>
 * Email  : <EMAIL>
 * Date   : 2020/4/13/15:49
 */
public class UpdateCheckPhotoResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"bf_guid":"9cd499f1-cebd-4383-b29b-86245753e124","bf_md5":"ccb278805bf00cc0b4323254f44933c8","bf_path":"/IssueImages/2020/4/13/dbb4900e-865a-40e0-a0d3-60cdd189c30e.jpeg","bf_filename":"JPEG_20200413_154742.jpeg"}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * bf_guid : 9cd499f1-cebd-4383-b29b-86245753e124
     * bf_md5 : ccb278805bf00cc0b4323254f44933c8
     * bf_path : /IssueImages/2020/4/13/dbb4900e-865a-40e0-a0d3-60cdd189c30e.jpeg
     * bf_filename : JPEG_20200413_154742.jpeg
     */

    private String bf_guid;
    private String bf_md5;
    private String bf_path;
    private String bf_filename;

    public String getBf_guid() {
      return bf_guid;
    }

    public void setBf_guid(String bf_guid) {
      this.bf_guid = bf_guid;
    }

    public String getBf_md5() {
      return bf_md5;
    }

    public void setBf_md5(String bf_md5) {
      this.bf_md5 = bf_md5;
    }

    public String getBf_path() {
      return bf_path;
    }

    public void setBf_path(String bf_path) {
      this.bf_path = bf_path;
    }

    public String getBf_filename() {
      return bf_filename;
    }

    public void setBf_filename(String bf_filename) {
      this.bf_filename = bf_filename;
    }
  }
}
