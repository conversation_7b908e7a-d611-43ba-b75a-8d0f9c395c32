package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

public class JsViewPoistionResult implements Serializable {

  /**
   * cameraInof : {"position":{"x":12.734097814882949,"y":2.8702934631915475,"z":-10.124770348638856},"rotation":{"_order":"YXZ","_x":0.054823727908683346,"_y":2.3132082271203958,"_z":0},"target":{"x":-2.4315106868743896,"y":3.999804735183715,"z":3.7900103628635406}}
   * clipInof : null
   * hidedElement : []
   * isolateElelement : []
   */

  private CameraInofBean cameraInof;
  private Object clipInof;
  private List<?> hidedElement;
  private List<?> isolateElelement;

  public CameraInofBean getCameraInof() {
    return cameraInof;
  }

  public void setCameraInof(CameraInofBean cameraInof) {
    this.cameraInof = cameraInof;
  }

  public Object getClipInof() {
    return clipInof;
  }

  public void setClipInof(Object clipInof) {
    this.clipInof = clipInof;
  }

  public List<?> getHidedElement() {
    return hidedElement;
  }

  public void setHidedElement(List<?> hidedElement) {
    this.hidedElement = hidedElement;
  }

  public List<?> getIsolateElelement() {
    return isolateElelement;
  }

  public void setIsolateElelement(List<?> isolateElelement) {
    this.isolateElelement = isolateElelement;
  }

  public static class CameraInofBean implements Serializable{
    /**
     * position : {"x":12.734097814882949,"y":2.8702934631915475,"z":-10.124770348638856}
     * rotation : {"_order":"YXZ","_x":0.054823727908683346,"_y":2.3132082271203958,"_z":0}
     * target : {"x":-2.4315106868743896,"y":3.999804735183715,"z":3.7900103628635406}
     */

    private PositionBean position;
    private RotationBean rotation;
    private TargetBean target;

    public PositionBean getPosition() {
      return position;
    }

    public void setPosition(PositionBean position) {
      this.position = position;
    }

    public RotationBean getRotation() {
      return rotation;
    }

    public void setRotation(RotationBean rotation) {
      this.rotation = rotation;
    }

    public TargetBean getTarget() {
      return target;
    }

    public void setTarget(TargetBean target) {
      this.target = target;
    }

    public static class PositionBean implements Serializable{
      /**
       * x : 12.734097814882949
       * y : 2.8702934631915475
       * z : -10.124770348638856
       */

      private String x;
      private String y;
      private String z;

      public String getX() {
        return x;
      }

      public void setX(String x) {
        this.x = x;
      }

      public String getY() {
        return y;
      }

      public void setY(String y) {
        this.y = y;
      }

      public String getZ() {
        return z;
      }

      public void setZ(String z) {
        this.z = z;
      }
    }

    public static class RotationBean implements Serializable{
      /**
       * _order : YXZ
       * _x : 0.054823727908683346
       * _y : 2.3132082271203958
       * _z : 0
       */

      private String _order;
      private String _x;
      private String _y;
      private String _z;

      public String get_order() {
        return _order;
      }

      public void set_order(String _order) {
        this._order = _order;
      }

      public String get_x() {
        return _x;
      }

      public void set_x(String _x) {
        this._x = _x;
      }

      public String get_y() {
        return _y;
      }

      public void set_y(String _y) {
        this._y = _y;
      }

      public String get_z() {
        return _z;
      }

      public void set_z(String _z) {
        this._z = _z;
      }
    }

    public static class TargetBean implements Serializable{
      /**
       * x : -2.4315106868743896
       * y : 3.999804735183715
       * z : 3.7900103628635406
       */

      private String x;
      private String y;
      private String z;

      public String getX() {
        return x;
      }

      public void setX(String x) {
        this.x = x;
      }

      public String getY() {
        return y;
      }

      public void setY(String y) {
        this.y = y;
      }

      public String getZ() {
        return z;
      }

      public void setZ(String z) {
        this.z = z;
      }
    }
  }
}
