package com.probim.bimenew.result;

import java.util.List;

public class IssueTagsResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"it_guid":"0b412982-8d9f-47ae-a72e-0f232503a469","it_name":"12","it_color":"rgba(86, 98, 112, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"8f59127c-a053-495b-b1d9-3506cc7faf37","it_name":"t123","it_color":"rgba(29, 164, 140, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"a243e160-8795-4f73-8007-6e18aaadd5bc","it_name":"严重的","it_color":"rgba(184, 158, 123, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"bf50c859-cb2f-456e-a8f9-d4762474556f","it_name":"t1123","it_color":"rgba(184, 158, 123, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"df0eb10a-3dc8-470a-a05c-f875e8239a67","it_name":"1","it_color":"rgba(29, 164, 140, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"e30fc831-82bc-46bf-b976-83b03c05dd02","it_name":"t661","it_color":"rgba(29, 164, 140, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"},{"it_guid":"f768c43c-91b2-4289-b499-0452a0d0a75a","it_name":"ccc1a2","it_color":"rgba(115, 113, 157, 1)","it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * it_guid : 0b412982-8d9f-47ae-a72e-0f232503a469
     * it_name : 12
     * it_color : rgba(86, 98, 112, 1)
     * it_organizeId : 48617e7b-07f2-4748-9199-238af8f2bfc6
     */

    private String it_guid;
    private String it_name;
    private String it_color;
    private String it_organizeId;

    public String getIt_guid() {
      return it_guid;
    }

    public void setIt_guid(String it_guid) {
      this.it_guid = it_guid;
    }

    public String getIt_name() {
      return it_name;
    }

    public void setIt_name(String it_name) {
      this.it_name = it_name;
    }

    public String getIt_color() {
      return it_color;
    }

    public void setIt_color(String it_color) {
      this.it_color = it_color;
    }

    public String getIt_organizeId() {
      return it_organizeId;
    }

    public void setIt_organizeId(String it_organizeId) {
      this.it_organizeId = it_organizeId;
    }
  }
}
