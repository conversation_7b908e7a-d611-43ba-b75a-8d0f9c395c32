package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/8/18/16:28
 */
public class CheckDetailsDto implements Serializable {

    @SerializedName("Ret")
    private Integer ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataBean data;

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean implements Serializable{
        @SerializedName("List")
        private ListBean list;

        public ListBean getList() {
            return list;
        }

        public void setList(ListBean list) {
            this.list = list;
        }

        public static class ListBean implements Serializable{
            private String aede_severitylevel;
            private String aedt_name;
            @SerializedName("CheckerName")
            private String checkerName;
            @SerializedName("ExamineDate")
            private String examineDate;
            @SerializedName("ExamineRemark")
            private String examineRemark;
            @SerializedName("ExamineResult")
            private String examineResult;
            @SerializedName("PrincipalID")
            private String principalID;
            @SerializedName("RectificateDate")
            private String rectificateDate;
            @SerializedName("RelationMemberID")
            private String relationMemberID;
            @SerializedName("RelationMember_Users")
            private List<RelationMemberUsersBean> relationMember_Users;
            @SerializedName("Principal_Users")
            private List<PrincipalUsersBean> principal_Users;
            @SerializedName("RecordWithAttachments")
            private List<RecordWithAttachmentsBean> recordWithAttachments;
            @SerializedName("Materials")
            private List<MaterialsBean> materials;
            @SerializedName("Tasks")
            private List<TasksBean> tasks;
            private List<ImgesDTO> imges;
            @SerializedName("ExamineResult_CH")
            private String examineResult_CH;
            @SerializedName("User_InPrincipal")
            private Boolean user_InPrincipal;
            @SerializedName("User_InRelation")
            private Boolean user_InRelation;
            private String aede_checkeruserids;
            @SerializedName("User_IsChecker")
            private Boolean user_IsChecker;

            public String getAede_severitylevel() {
                return aede_severitylevel;
            }

            public void setAede_severitylevel(String aede_severitylevel) {
                this.aede_severitylevel = aede_severitylevel;
            }

            public String getAedt_name() {
                return aedt_name;
            }

            public void setAedt_name(String aedt_name) {
                this.aedt_name = aedt_name;
            }

            public String getCheckerName() {
                return checkerName;
            }

            public void setCheckerName(String checkerName) {
                this.checkerName = checkerName;
            }

            public String getExamineDate() {
                return examineDate;
            }

            public void setExamineDate(String examineDate) {
                this.examineDate = examineDate;
            }

            public String getExamineRemark() {
                return examineRemark;
            }

            public void setExamineRemark(String examineRemark) {
                this.examineRemark = examineRemark;
            }

            public String getExamineResult() {
                return examineResult;
            }

            public void setExamineResult(String examineResult) {
                this.examineResult = examineResult;
            }

            public String getPrincipalID() {
                return principalID;
            }

            public void setPrincipalID(String principalID) {
                this.principalID = principalID;
            }

            public String getRectificateDate() {
                return rectificateDate;
            }

            public void setRectificateDate(String rectificateDate) {
                this.rectificateDate = rectificateDate;
            }

            public String getRelationMemberID() {
                return relationMemberID;
            }

            public void setRelationMemberID(String relationMemberID) {
                this.relationMemberID = relationMemberID;
            }

            public List<RelationMemberUsersBean> getRelationMember_Users() {
                return relationMember_Users;
            }

            public void setRelationMember_Users(List<RelationMemberUsersBean> relationMember_Users) {
                this.relationMember_Users = relationMember_Users;
            }

            public List<PrincipalUsersBean> getPrincipal_Users() {
                return principal_Users;
            }

            public void setPrincipal_Users(List<PrincipalUsersBean> principal_Users) {
                this.principal_Users = principal_Users;
            }

            public List<RecordWithAttachmentsBean> getRecordWithAttachments() {
                return recordWithAttachments;
            }

            public void setRecordWithAttachments(List<RecordWithAttachmentsBean> recordWithAttachments) {
                this.recordWithAttachments = recordWithAttachments;
            }

            public List<MaterialsBean> getMaterials() {
                return materials;
            }

            public void setMaterials(List<MaterialsBean> materials) {
                this.materials = materials;
            }

            public List<TasksBean> getTasks() {
                return tasks;
            }

            public void setTasks(List<TasksBean> tasks) {
                this.tasks = tasks;
            }

            public String getExamineResult_CH() {
                return examineResult_CH;
            }

            public void setExamineResult_CH(String examineResult_CH) {
                this.examineResult_CH = examineResult_CH;
            }

            public Boolean getUser_InPrincipal() {
                return user_InPrincipal;
            }

            public void setUser_InPrincipal(Boolean user_InPrincipal) {
                this.user_InPrincipal = user_InPrincipal;
            }

            public Boolean getUser_InRelation() {
                return user_InRelation;
            }

            public void setUser_InRelation(Boolean user_InRelation) {
                this.user_InRelation = user_InRelation;
            }

            public String getAede_checkeruserids() {
                return aede_checkeruserids;
            }

            public void setAede_checkeruserids(String aede_checkeruserids) {
                this.aede_checkeruserids = aede_checkeruserids;
            }

            public Boolean getUser_IsChecker() {
                return user_IsChecker;
            }

            public void setUser_IsChecker(Boolean user_IsChecker) {
                this.user_IsChecker = user_IsChecker;
            }

            public static class RelationMemberUsersBean implements Serializable{
                @SerializedName("UserId")
                private String userId;
                @SerializedName("EnCode")
                private Object enCode;
                @SerializedName("Account")
                private String account;
                @SerializedName("Password")
                private String password;
                @SerializedName("Secretkey")
                private String secretkey;
                @SerializedName("RealName")
                private String realName;
                @SerializedName("NickName")
                private Object nickName;
                @SerializedName("HeadIcon")
                private Object headIcon;
                @SerializedName("QuickQuery")
                private Object quickQuery;
                @SerializedName("SimpleSpelling")
                private Object simpleSpelling;
                @SerializedName("Gender")
                private Object gender;
                @SerializedName("Birthday")
                private Object birthday;
                @SerializedName("Mobile")
                private Object mobile;
                @SerializedName("Telephone")
                private Object telephone;
                @SerializedName("Email")
                private String email;
                @SerializedName("OICQ")
                private Object oICQ;
                @SerializedName("WeChat")
                private Object weChat;
                @SerializedName("MSN")
                private Object mSN;
                @SerializedName("ManagerId")
                private Object managerId;
                @SerializedName("Manager")
                private Object manager;
                @SerializedName("OrganizeId")
                private String organizeId;
                @SerializedName("DepartmentId")
                private Object departmentId;
                @SerializedName("RoleId")
                private Object roleId;
                @SerializedName("DutyId")
                private Object dutyId;
                @SerializedName("DutyName")
                private Object dutyName;
                @SerializedName("PostId")
                private Object postId;
                @SerializedName("PostName")
                private Object postName;
                @SerializedName("WorkGroupId")
                private Object workGroupId;
                @SerializedName("SecurityLevel")
                private Object securityLevel;
                @SerializedName("UserOnLine")
                private Object userOnLine;
                @SerializedName("OpenId")
                private Object openId;
                @SerializedName("Question")
                private Object question;
                @SerializedName("AnswerQuestion")
                private Object answerQuestion;
                @SerializedName("CheckOnLine")
                private Object checkOnLine;
                @SerializedName("AllowStartTime")
                private Object allowStartTime;
                @SerializedName("AllowEndTime")
                private Object allowEndTime;
                @SerializedName("LockStartDate")
                private Object lockStartDate;
                @SerializedName("LockEndDate")
                private Object lockEndDate;
                @SerializedName("FirstVisit")
                private Object firstVisit;
                @SerializedName("PreviousVisit")
                private Object previousVisit;
                @SerializedName("LastVisit")
                private Object lastVisit;
                @SerializedName("LogOnCount")
                private Object logOnCount;
                @SerializedName("SortCode")
                private Object sortCode;
                @SerializedName("DeleteMark")
                private Integer deleteMark;
                @SerializedName("EnabledMark")
                private Integer enabledMark;
                @SerializedName("Description")
                private Object description;
                @SerializedName("CreateDate")
                private String createDate;
                @SerializedName("CreateUserId")
                private String createUserId;
                @SerializedName("CreateUserName")
                private String createUserName;
                @SerializedName("ModifyDate")
                private Object modifyDate;
                @SerializedName("ModifyUserId")
                private Object modifyUserId;
                @SerializedName("ModifyUserName")
                private Object modifyUserName;
                @SerializedName("UserType")
                private Object userType;
                @SerializedName("DataFrom")
                private String dataFrom;

                public String getUserId() {
                    return userId;
                }

                public void setUserId(String userId) {
                    this.userId = userId;
                }

                public Object getEnCode() {
                    return enCode;
                }

                public void setEnCode(Object enCode) {
                    this.enCode = enCode;
                }

                public String getAccount() {
                    return account;
                }

                public void setAccount(String account) {
                    this.account = account;
                }

                public String getPassword() {
                    return password;
                }

                public void setPassword(String password) {
                    this.password = password;
                }

                public String getSecretkey() {
                    return secretkey;
                }

                public void setSecretkey(String secretkey) {
                    this.secretkey = secretkey;
                }

                public String getRealName() {
                    return realName;
                }

                public void setRealName(String realName) {
                    this.realName = realName;
                }

                public Object getNickName() {
                    return nickName;
                }

                public void setNickName(Object nickName) {
                    this.nickName = nickName;
                }

                public Object getHeadIcon() {
                    return headIcon;
                }

                public void setHeadIcon(Object headIcon) {
                    this.headIcon = headIcon;
                }

                public Object getQuickQuery() {
                    return quickQuery;
                }

                public void setQuickQuery(Object quickQuery) {
                    this.quickQuery = quickQuery;
                }

                public Object getSimpleSpelling() {
                    return simpleSpelling;
                }

                public void setSimpleSpelling(Object simpleSpelling) {
                    this.simpleSpelling = simpleSpelling;
                }

                public Object getGender() {
                    return gender;
                }

                public void setGender(Object gender) {
                    this.gender = gender;
                }

                public Object getBirthday() {
                    return birthday;
                }

                public void setBirthday(Object birthday) {
                    this.birthday = birthday;
                }

                public Object getMobile() {
                    return mobile;
                }

                public void setMobile(Object mobile) {
                    this.mobile = mobile;
                }

                public Object getTelephone() {
                    return telephone;
                }

                public void setTelephone(Object telephone) {
                    this.telephone = telephone;
                }

                public String getEmail() {
                    return email;
                }

                public void setEmail(String email) {
                    this.email = email;
                }

                public Object getOICQ() {
                    return oICQ;
                }

                public void setOICQ(Object oICQ) {
                    this.oICQ = oICQ;
                }

                public Object getWeChat() {
                    return weChat;
                }

                public void setWeChat(Object weChat) {
                    this.weChat = weChat;
                }

                public Object getMSN() {
                    return mSN;
                }

                public void setMSN(Object mSN) {
                    this.mSN = mSN;
                }

                public Object getManagerId() {
                    return managerId;
                }

                public void setManagerId(Object managerId) {
                    this.managerId = managerId;
                }

                public Object getManager() {
                    return manager;
                }

                public void setManager(Object manager) {
                    this.manager = manager;
                }

                public String getOrganizeId() {
                    return organizeId;
                }

                public void setOrganizeId(String organizeId) {
                    this.organizeId = organizeId;
                }

                public Object getDepartmentId() {
                    return departmentId;
                }

                public void setDepartmentId(Object departmentId) {
                    this.departmentId = departmentId;
                }

                public Object getRoleId() {
                    return roleId;
                }

                public void setRoleId(Object roleId) {
                    this.roleId = roleId;
                }

                public Object getDutyId() {
                    return dutyId;
                }

                public void setDutyId(Object dutyId) {
                    this.dutyId = dutyId;
                }

                public Object getDutyName() {
                    return dutyName;
                }

                public void setDutyName(Object dutyName) {
                    this.dutyName = dutyName;
                }

                public Object getPostId() {
                    return postId;
                }

                public void setPostId(Object postId) {
                    this.postId = postId;
                }

                public Object getPostName() {
                    return postName;
                }

                public void setPostName(Object postName) {
                    this.postName = postName;
                }

                public Object getWorkGroupId() {
                    return workGroupId;
                }

                public void setWorkGroupId(Object workGroupId) {
                    this.workGroupId = workGroupId;
                }

                public Object getSecurityLevel() {
                    return securityLevel;
                }

                public void setSecurityLevel(Object securityLevel) {
                    this.securityLevel = securityLevel;
                }

                public Object getUserOnLine() {
                    return userOnLine;
                }

                public void setUserOnLine(Object userOnLine) {
                    this.userOnLine = userOnLine;
                }

                public Object getOpenId() {
                    return openId;
                }

                public void setOpenId(Object openId) {
                    this.openId = openId;
                }

                public Object getQuestion() {
                    return question;
                }

                public void setQuestion(Object question) {
                    this.question = question;
                }

                public Object getAnswerQuestion() {
                    return answerQuestion;
                }

                public void setAnswerQuestion(Object answerQuestion) {
                    this.answerQuestion = answerQuestion;
                }

                public Object getCheckOnLine() {
                    return checkOnLine;
                }

                public void setCheckOnLine(Object checkOnLine) {
                    this.checkOnLine = checkOnLine;
                }

                public Object getAllowStartTime() {
                    return allowStartTime;
                }

                public void setAllowStartTime(Object allowStartTime) {
                    this.allowStartTime = allowStartTime;
                }

                public Object getAllowEndTime() {
                    return allowEndTime;
                }

                public void setAllowEndTime(Object allowEndTime) {
                    this.allowEndTime = allowEndTime;
                }

                public Object getLockStartDate() {
                    return lockStartDate;
                }

                public void setLockStartDate(Object lockStartDate) {
                    this.lockStartDate = lockStartDate;
                }

                public Object getLockEndDate() {
                    return lockEndDate;
                }

                public void setLockEndDate(Object lockEndDate) {
                    this.lockEndDate = lockEndDate;
                }

                public Object getFirstVisit() {
                    return firstVisit;
                }

                public void setFirstVisit(Object firstVisit) {
                    this.firstVisit = firstVisit;
                }

                public Object getPreviousVisit() {
                    return previousVisit;
                }

                public void setPreviousVisit(Object previousVisit) {
                    this.previousVisit = previousVisit;
                }

                public Object getLastVisit() {
                    return lastVisit;
                }

                public void setLastVisit(Object lastVisit) {
                    this.lastVisit = lastVisit;
                }

                public Object getLogOnCount() {
                    return logOnCount;
                }

                public void setLogOnCount(Object logOnCount) {
                    this.logOnCount = logOnCount;
                }

                public Object getSortCode() {
                    return sortCode;
                }

                public void setSortCode(Object sortCode) {
                    this.sortCode = sortCode;
                }

                public Integer getDeleteMark() {
                    return deleteMark;
                }

                public void setDeleteMark(Integer deleteMark) {
                    this.deleteMark = deleteMark;
                }

                public Integer getEnabledMark() {
                    return enabledMark;
                }

                public void setEnabledMark(Integer enabledMark) {
                    this.enabledMark = enabledMark;
                }

                public Object getDescription() {
                    return description;
                }

                public void setDescription(Object description) {
                    this.description = description;
                }

                public String getCreateDate() {
                    return createDate;
                }

                public void setCreateDate(String createDate) {
                    this.createDate = createDate;
                }

                public String getCreateUserId() {
                    return createUserId;
                }

                public void setCreateUserId(String createUserId) {
                    this.createUserId = createUserId;
                }

                public String getCreateUserName() {
                    return createUserName;
                }

                public void setCreateUserName(String createUserName) {
                    this.createUserName = createUserName;
                }

                public Object getModifyDate() {
                    return modifyDate;
                }

                public void setModifyDate(Object modifyDate) {
                    this.modifyDate = modifyDate;
                }

                public Object getModifyUserId() {
                    return modifyUserId;
                }

                public void setModifyUserId(Object modifyUserId) {
                    this.modifyUserId = modifyUserId;
                }

                public Object getModifyUserName() {
                    return modifyUserName;
                }

                public void setModifyUserName(Object modifyUserName) {
                    this.modifyUserName = modifyUserName;
                }

                public Object getUserType() {
                    return userType;
                }

                public void setUserType(Object userType) {
                    this.userType = userType;
                }

                public String getDataFrom() {
                    return dataFrom;
                }

                public void setDataFrom(String dataFrom) {
                    this.dataFrom = dataFrom;
                }
            }

            public static class PrincipalUsersBean implements Serializable{
                @SerializedName("UserId")
                private String userId;
                @SerializedName("EnCode")
                private Object enCode;
                @SerializedName("Account")
                private String account;
                @SerializedName("Password")
                private String password;
                @SerializedName("Secretkey")
                private String secretkey;
                @SerializedName("RealName")
                private String realName;
                @SerializedName("NickName")
                private Object nickName;
                @SerializedName("HeadIcon")
                private Object headIcon;
                @SerializedName("QuickQuery")
                private Object quickQuery;
                @SerializedName("SimpleSpelling")
                private Object simpleSpelling;
                @SerializedName("Gender")
                private Integer gender;
                @SerializedName("Birthday")
                private Object birthday;
                @SerializedName("Mobile")
                private String mobile;
                @SerializedName("Telephone")
                private Object telephone;
                @SerializedName("Email")
                private String email;
                @SerializedName("OICQ")
                private Object oICQ;
                @SerializedName("WeChat")
                private Object weChat;
                @SerializedName("MSN")
                private Object mSN;
                @SerializedName("ManagerId")
                private String managerId;
                @SerializedName("Manager")
                private Object manager;
                @SerializedName("OrganizeId")
                private String organizeId;
                @SerializedName("DepartmentId")
                private String departmentId;
                @SerializedName("RoleId")
                private String roleId;
                @SerializedName("DutyId")
                private String dutyId;
                @SerializedName("DutyName")
                private Object dutyName;
                @SerializedName("PostId")
                private String postId;
                @SerializedName("PostName")
                private Object postName;
                @SerializedName("WorkGroupId")
                private Object workGroupId;
                @SerializedName("SecurityLevel")
                private Object securityLevel;
                @SerializedName("UserOnLine")
                private Integer userOnLine;
                @SerializedName("OpenId")
                private Object openId;
                @SerializedName("Question")
                private Object question;
                @SerializedName("AnswerQuestion")
                private Object answerQuestion;
                @SerializedName("CheckOnLine")
                private Object checkOnLine;
                @SerializedName("AllowStartTime")
                private Object allowStartTime;
                @SerializedName("AllowEndTime")
                private Object allowEndTime;
                @SerializedName("LockStartDate")
                private Object lockStartDate;
                @SerializedName("LockEndDate")
                private Object lockEndDate;
                @SerializedName("FirstVisit")
                private Object firstVisit;
                @SerializedName("PreviousVisit")
                private String previousVisit;
                @SerializedName("LastVisit")
                private String lastVisit;
                @SerializedName("LogOnCount")
                private Integer logOnCount;
                @SerializedName("SortCode")
                private Object sortCode;
                @SerializedName("DeleteMark")
                private Integer deleteMark;
                @SerializedName("EnabledMark")
                private Integer enabledMark;
                @SerializedName("Description")
                private Object description;
                @SerializedName("CreateDate")
                private String createDate;
                @SerializedName("CreateUserId")
                private String createUserId;
                @SerializedName("CreateUserName")
                private String createUserName;
                @SerializedName("ModifyDate")
                private String modifyDate;
                @SerializedName("ModifyUserId")
                private String modifyUserId;
                @SerializedName("ModifyUserName")
                private String modifyUserName;
                @SerializedName("UserType")
                private Object userType;
                @SerializedName("DataFrom")
                private Object dataFrom;

                public String getUserId() {
                    return userId;
                }

                public void setUserId(String userId) {
                    this.userId = userId;
                }

                public Object getEnCode() {
                    return enCode;
                }

                public void setEnCode(Object enCode) {
                    this.enCode = enCode;
                }

                public String getAccount() {
                    return account;
                }

                public void setAccount(String account) {
                    this.account = account;
                }

                public String getPassword() {
                    return password;
                }

                public void setPassword(String password) {
                    this.password = password;
                }

                public String getSecretkey() {
                    return secretkey;
                }

                public void setSecretkey(String secretkey) {
                    this.secretkey = secretkey;
                }

                public String getRealName() {
                    return realName;
                }

                public void setRealName(String realName) {
                    this.realName = realName;
                }

                public Object getNickName() {
                    return nickName;
                }

                public void setNickName(Object nickName) {
                    this.nickName = nickName;
                }

                public Object getHeadIcon() {
                    return headIcon;
                }

                public void setHeadIcon(Object headIcon) {
                    this.headIcon = headIcon;
                }

                public Object getQuickQuery() {
                    return quickQuery;
                }

                public void setQuickQuery(Object quickQuery) {
                    this.quickQuery = quickQuery;
                }

                public Object getSimpleSpelling() {
                    return simpleSpelling;
                }

                public void setSimpleSpelling(Object simpleSpelling) {
                    this.simpleSpelling = simpleSpelling;
                }

                public Integer getGender() {
                    return gender;
                }

                public void setGender(Integer gender) {
                    this.gender = gender;
                }

                public Object getBirthday() {
                    return birthday;
                }

                public void setBirthday(Object birthday) {
                    this.birthday = birthday;
                }

                public String getMobile() {
                    return mobile;
                }

                public void setMobile(String mobile) {
                    this.mobile = mobile;
                }

                public Object getTelephone() {
                    return telephone;
                }

                public void setTelephone(Object telephone) {
                    this.telephone = telephone;
                }

                public String getEmail() {
                    return email;
                }

                public void setEmail(String email) {
                    this.email = email;
                }

                public Object getOICQ() {
                    return oICQ;
                }

                public void setOICQ(Object oICQ) {
                    this.oICQ = oICQ;
                }

                public Object getWeChat() {
                    return weChat;
                }

                public void setWeChat(Object weChat) {
                    this.weChat = weChat;
                }

                public Object getMSN() {
                    return mSN;
                }

                public void setMSN(Object mSN) {
                    this.mSN = mSN;
                }

                public String getManagerId() {
                    return managerId;
                }

                public void setManagerId(String managerId) {
                    this.managerId = managerId;
                }

                public Object getManager() {
                    return manager;
                }

                public void setManager(Object manager) {
                    this.manager = manager;
                }

                public String getOrganizeId() {
                    return organizeId;
                }

                public void setOrganizeId(String organizeId) {
                    this.organizeId = organizeId;
                }

                public String getDepartmentId() {
                    return departmentId;
                }

                public void setDepartmentId(String departmentId) {
                    this.departmentId = departmentId;
                }

                public String getRoleId() {
                    return roleId;
                }

                public void setRoleId(String roleId) {
                    this.roleId = roleId;
                }

                public String getDutyId() {
                    return dutyId;
                }

                public void setDutyId(String dutyId) {
                    this.dutyId = dutyId;
                }

                public Object getDutyName() {
                    return dutyName;
                }

                public void setDutyName(Object dutyName) {
                    this.dutyName = dutyName;
                }

                public String getPostId() {
                    return postId;
                }

                public void setPostId(String postId) {
                    this.postId = postId;
                }

                public Object getPostName() {
                    return postName;
                }

                public void setPostName(Object postName) {
                    this.postName = postName;
                }

                public Object getWorkGroupId() {
                    return workGroupId;
                }

                public void setWorkGroupId(Object workGroupId) {
                    this.workGroupId = workGroupId;
                }

                public Object getSecurityLevel() {
                    return securityLevel;
                }

                public void setSecurityLevel(Object securityLevel) {
                    this.securityLevel = securityLevel;
                }

                public Integer getUserOnLine() {
                    return userOnLine;
                }

                public void setUserOnLine(Integer userOnLine) {
                    this.userOnLine = userOnLine;
                }

                public Object getOpenId() {
                    return openId;
                }

                public void setOpenId(Object openId) {
                    this.openId = openId;
                }

                public Object getQuestion() {
                    return question;
                }

                public void setQuestion(Object question) {
                    this.question = question;
                }

                public Object getAnswerQuestion() {
                    return answerQuestion;
                }

                public void setAnswerQuestion(Object answerQuestion) {
                    this.answerQuestion = answerQuestion;
                }

                public Object getCheckOnLine() {
                    return checkOnLine;
                }

                public void setCheckOnLine(Object checkOnLine) {
                    this.checkOnLine = checkOnLine;
                }

                public Object getAllowStartTime() {
                    return allowStartTime;
                }

                public void setAllowStartTime(Object allowStartTime) {
                    this.allowStartTime = allowStartTime;
                }

                public Object getAllowEndTime() {
                    return allowEndTime;
                }

                public void setAllowEndTime(Object allowEndTime) {
                    this.allowEndTime = allowEndTime;
                }

                public Object getLockStartDate() {
                    return lockStartDate;
                }

                public void setLockStartDate(Object lockStartDate) {
                    this.lockStartDate = lockStartDate;
                }

                public Object getLockEndDate() {
                    return lockEndDate;
                }

                public void setLockEndDate(Object lockEndDate) {
                    this.lockEndDate = lockEndDate;
                }

                public Object getFirstVisit() {
                    return firstVisit;
                }

                public void setFirstVisit(Object firstVisit) {
                    this.firstVisit = firstVisit;
                }

                public String getPreviousVisit() {
                    return previousVisit;
                }

                public void setPreviousVisit(String previousVisit) {
                    this.previousVisit = previousVisit;
                }

                public String getLastVisit() {
                    return lastVisit;
                }

                public void setLastVisit(String lastVisit) {
                    this.lastVisit = lastVisit;
                }

                public Integer getLogOnCount() {
                    return logOnCount;
                }

                public void setLogOnCount(Integer logOnCount) {
                    this.logOnCount = logOnCount;
                }

                public Object getSortCode() {
                    return sortCode;
                }

                public void setSortCode(Object sortCode) {
                    this.sortCode = sortCode;
                }

                public Integer getDeleteMark() {
                    return deleteMark;
                }

                public void setDeleteMark(Integer deleteMark) {
                    this.deleteMark = deleteMark;
                }

                public Integer getEnabledMark() {
                    return enabledMark;
                }

                public void setEnabledMark(Integer enabledMark) {
                    this.enabledMark = enabledMark;
                }

                public Object getDescription() {
                    return description;
                }

                public void setDescription(Object description) {
                    this.description = description;
                }

                public String getCreateDate() {
                    return createDate;
                }

                public void setCreateDate(String createDate) {
                    this.createDate = createDate;
                }

                public String getCreateUserId() {
                    return createUserId;
                }

                public void setCreateUserId(String createUserId) {
                    this.createUserId = createUserId;
                }

                public String getCreateUserName() {
                    return createUserName;
                }

                public void setCreateUserName(String createUserName) {
                    this.createUserName = createUserName;
                }

                public String getModifyDate() {
                    return modifyDate;
                }

                public void setModifyDate(String modifyDate) {
                    this.modifyDate = modifyDate;
                }

                public String getModifyUserId() {
                    return modifyUserId;
                }

                public void setModifyUserId(String modifyUserId) {
                    this.modifyUserId = modifyUserId;
                }

                public String getModifyUserName() {
                    return modifyUserName;
                }

                public void setModifyUserName(String modifyUserName) {
                    this.modifyUserName = modifyUserName;
                }

                public Object getUserType() {
                    return userType;
                }

                public void setUserType(Object userType) {
                    this.userType = userType;
                }

                public Object getDataFrom() {
                    return dataFrom;
                }

                public void setDataFrom(Object dataFrom) {
                    this.dataFrom = dataFrom;
                }
            }

            public static class RecordWithAttachmentsBean implements Serializable{
                @SerializedName("Attachments")
                private List<AttachmentsBean> attachments;
                @SerializedName("RectificationID")
                private String rectificationID;
                @SerializedName("RectificationOperatorID")
                private String rectificationOperatorID;
                @SerializedName("RectificationOperator")
                private String rectificationOperator;
                @SerializedName("RectificationOperateFlag")
                private Integer rectificationOperateFlag;
                @SerializedName("RectificationCheckDate")
                private String rectificationCheckDate;
                @SerializedName("RectificationCheckResult")
                private String rectificationCheckResult;
                @SerializedName("RectificationRemark")
                private String rectificationRemark;
                @SerializedName("IsDel")
                private Object isDel;
                @SerializedName("ExamineID")
                private String examineID;
                @SerializedName("CreateDate")
                private String createDate;
                private String aer_counterpart;

                public List<AttachmentsBean> getAttachments() {
                    return attachments;
                }

                public void setAttachments(List<AttachmentsBean> attachments) {
                    this.attachments = attachments;
                }

                public String getRectificationID() {
                    return rectificationID;
                }

                public void setRectificationID(String rectificationID) {
                    this.rectificationID = rectificationID;
                }

                public String getRectificationOperatorID() {
                    return rectificationOperatorID;
                }

                public void setRectificationOperatorID(String rectificationOperatorID) {
                    this.rectificationOperatorID = rectificationOperatorID;
                }

                public String getRectificationOperator() {
                    return rectificationOperator;
                }

                public void setRectificationOperator(String rectificationOperator) {
                    this.rectificationOperator = rectificationOperator;
                }

                public Integer getRectificationOperateFlag() {
                    return rectificationOperateFlag;
                }

                public void setRectificationOperateFlag(Integer rectificationOperateFlag) {
                    this.rectificationOperateFlag = rectificationOperateFlag;
                }

                public String getRectificationCheckDate() {
                    return rectificationCheckDate;
                }

                public void setRectificationCheckDate(String rectificationCheckDate) {
                    this.rectificationCheckDate = rectificationCheckDate;
                }

                public String getRectificationCheckResult() {
                    return rectificationCheckResult;
                }

                public void setRectificationCheckResult(String rectificationCheckResult) {
                    this.rectificationCheckResult = rectificationCheckResult;
                }

                public String getRectificationRemark() {
                    return rectificationRemark;
                }

                public void setRectificationRemark(String rectificationRemark) {
                    this.rectificationRemark = rectificationRemark;
                }

                public Object getIsDel() {
                    return isDel;
                }

                public void setIsDel(Object isDel) {
                    this.isDel = isDel;
                }

                public String getExamineID() {
                    return examineID;
                }

                public void setExamineID(String examineID) {
                    this.examineID = examineID;
                }

                public String getCreateDate() {
                    return createDate;
                }

                public void setCreateDate(String createDate) {
                    this.createDate = createDate;
                }

                public String getAer_counterpart() {
                    return aer_counterpart;
                }

                public void setAer_counterpart(String aer_counterpart) {
                    this.aer_counterpart = aer_counterpart;
                }

                public static class AttachmentsBean implements Serializable{
                    @SerializedName("ExamineAttachmentID")
                    private String examineAttachmentID;
                    @SerializedName("ExamineID")
                    private String examineID;
                    @SerializedName("AttachmentType")
                    private String attachmentType;
                    @SerializedName("AttachmentName")
                    private String attachmentName;
                    @SerializedName("AttachmentUrl")
                    private String attachmentUrl;
                    @SerializedName("UploadDate")
                    private String uploadDate;
                    @SerializedName("CheckFlag")
                    private Object checkFlag;
                    @SerializedName("IsDel")
                    private Object isDel;

                    public String getExamineAttachmentID() {
                        return examineAttachmentID;
                    }

                    public void setExamineAttachmentID(String examineAttachmentID) {
                        this.examineAttachmentID = examineAttachmentID;
                    }

                    public String getExamineID() {
                        return examineID;
                    }

                    public void setExamineID(String examineID) {
                        this.examineID = examineID;
                    }

                    public String getAttachmentType() {
                        return attachmentType;
                    }

                    public void setAttachmentType(String attachmentType) {
                        this.attachmentType = attachmentType;
                    }

                    public String getAttachmentName() {
                        return attachmentName;
                    }

                    public void setAttachmentName(String attachmentName) {
                        this.attachmentName = attachmentName;
                    }

                    public String getAttachmentUrl() {
                        return attachmentUrl;
                    }

                    public void setAttachmentUrl(String attachmentUrl) {
                        this.attachmentUrl = attachmentUrl;
                    }

                    public String getUploadDate() {
                        return uploadDate;
                    }

                    public void setUploadDate(String uploadDate) {
                        this.uploadDate = uploadDate;
                    }

                    public Object getCheckFlag() {
                        return checkFlag;
                    }

                    public void setCheckFlag(Object checkFlag) {
                        this.checkFlag = checkFlag;
                    }

                    public Object getIsDel() {
                        return isDel;
                    }

                    public void setIsDel(Object isDel) {
                        this.isDel = isDel;
                    }
                }
            }

            public static class MaterialsBean implements Serializable{
                private String bm_guid;
                private String bm_materialname;
                private String bm_materialcode;
                private String bm_materialmodel;
                private Integer bm_materialcount;
                private String bm_materialunit;
                private String bm_materialfac;
                private Object bm_planarrtime;
                private String bc_guid_materialtype;
                private Object bc_guid_materialstatus;
                private String bm_extjson;
                private String bm_updatetime;

                public String getBm_guid() {
                    return bm_guid;
                }

                public void setBm_guid(String bm_guid) {
                    this.bm_guid = bm_guid;
                }

                public String getBm_materialname() {
                    return bm_materialname;
                }

                public void setBm_materialname(String bm_materialname) {
                    this.bm_materialname = bm_materialname;
                }

                public String getBm_materialcode() {
                    return bm_materialcode;
                }

                public void setBm_materialcode(String bm_materialcode) {
                    this.bm_materialcode = bm_materialcode;
                }

                public String getBm_materialmodel() {
                    return bm_materialmodel;
                }

                public void setBm_materialmodel(String bm_materialmodel) {
                    this.bm_materialmodel = bm_materialmodel;
                }

                public Integer getBm_materialcount() {
                    return bm_materialcount;
                }

                public void setBm_materialcount(Integer bm_materialcount) {
                    this.bm_materialcount = bm_materialcount;
                }

                public String getBm_materialunit() {
                    return bm_materialunit;
                }

                public void setBm_materialunit(String bm_materialunit) {
                    this.bm_materialunit = bm_materialunit;
                }

                public String getBm_materialfac() {
                    return bm_materialfac;
                }

                public void setBm_materialfac(String bm_materialfac) {
                    this.bm_materialfac = bm_materialfac;
                }

                public Object getBm_planarrtime() {
                    return bm_planarrtime;
                }

                public void setBm_planarrtime(Object bm_planarrtime) {
                    this.bm_planarrtime = bm_planarrtime;
                }

                public String getBc_guid_materialtype() {
                    return bc_guid_materialtype;
                }

                public void setBc_guid_materialtype(String bc_guid_materialtype) {
                    this.bc_guid_materialtype = bc_guid_materialtype;
                }

                public Object getBc_guid_materialstatus() {
                    return bc_guid_materialstatus;
                }

                public void setBc_guid_materialstatus(Object bc_guid_materialstatus) {
                    this.bc_guid_materialstatus = bc_guid_materialstatus;
                }

                public String getBm_extjson() {
                    return bm_extjson;
                }

                public void setBm_extjson(String bm_extjson) {
                    this.bm_extjson = bm_extjson;
                }

                public String getBm_updatetime() {
                    return bm_updatetime;
                }

                public void setBm_updatetime(String bm_updatetime) {
                    this.bm_updatetime = bm_updatetime;
                }
            }

            public static class TasksBean implements Serializable{
                @SerializedName("UID_")
                private String uID_;
                @SerializedName("ID_")
                private Integer iD_;
                @SerializedName("NAME_")
                private String nAME_;
                @SerializedName("START_")
                private String sTART_;
                @SerializedName("FINISH_")
                private String fINISH_;
                @SerializedName("DURATION_")
                private Integer dURATION_;
                @SerializedName("WORK_")
                private Integer wORK_;
                @SerializedName("PERCENTCOMPLETE_")
                private Integer pERCENTCOMPLETE_;
                @SerializedName("MANUAL_")
                private Integer mANUAL_;
                @SerializedName("WEIGHT_")
                private Integer wEIGHT_;
                @SerializedName("CONSTRAINTTYPE_")
                private Integer cONSTRAINTTYPE_;
                @SerializedName("CONSTRAINTDATE_")
                private String cONSTRAINTDATE_;
                @SerializedName("MILESTONE_")
                private Object mILESTONE_;
                @SerializedName("SUMMARY_")
                private Integer sUMMARY_;
                @SerializedName("CRITICAL_")
                private Integer cRITICAL_;
                @SerializedName("PRIORITY_")
                private Object pRIORITY_;
                @SerializedName("NOTES_")
                private Object nOTES_;
                @SerializedName("DEPARTMENT_")
                private Object dEPARTMENT_;
                @SerializedName("PRINCIPAL_")
                private Object pRINCIPAL_;
                @SerializedName("PREDECESSORLINK_")
                private String pREDECESSORLINK_;
                @SerializedName("FIXEDDATE_")
                private Integer fIXEDDATE_;
                @SerializedName("PARENTTASKUID_")
                private String pARENTTASKUID_;
                @SerializedName("PROJECTUID_")
                private String pROJECTUID_;
                @SerializedName("ACTUALSTART_")
                private Object aCTUALSTART_;
                @SerializedName("ACTUALFINISH_")
                private Object aCTUALFINISH_;
                @SerializedName("ACTUALDURATION_")
                private Object aCTUALDURATION_;
                @SerializedName("ASSIGNMENTS_")
                private Object aSSIGNMENTS_;
                @SerializedName("WBS_")
                private Object wBS_;
                @SerializedName("CRITICAL2_")
                private Object cRITICAL2_;

                public String getUID_() {
                    return uID_;
                }

                public void setUID_(String uID_) {
                    this.uID_ = uID_;
                }

                public Integer getID_() {
                    return iD_;
                }

                public void setID_(Integer iD_) {
                    this.iD_ = iD_;
                }

                public String getNAME_() {
                    return nAME_;
                }

                public void setNAME_(String nAME_) {
                    this.nAME_ = nAME_;
                }

                public String getSTART_() {
                    return sTART_;
                }

                public void setSTART_(String sTART_) {
                    this.sTART_ = sTART_;
                }

                public String getFINISH_() {
                    return fINISH_;
                }

                public void setFINISH_(String fINISH_) {
                    this.fINISH_ = fINISH_;
                }

                public Integer getDURATION_() {
                    return dURATION_;
                }

                public void setDURATION_(Integer dURATION_) {
                    this.dURATION_ = dURATION_;
                }

                public Integer getWORK_() {
                    return wORK_;
                }

                public void setWORK_(Integer wORK_) {
                    this.wORK_ = wORK_;
                }

                public Integer getPERCENTCOMPLETE_() {
                    return pERCENTCOMPLETE_;
                }

                public void setPERCENTCOMPLETE_(Integer pERCENTCOMPLETE_) {
                    this.pERCENTCOMPLETE_ = pERCENTCOMPLETE_;
                }

                public Integer getMANUAL_() {
                    return mANUAL_;
                }

                public void setMANUAL_(Integer mANUAL_) {
                    this.mANUAL_ = mANUAL_;
                }

                public Integer getWEIGHT_() {
                    return wEIGHT_;
                }

                public void setWEIGHT_(Integer wEIGHT_) {
                    this.wEIGHT_ = wEIGHT_;
                }

                public Integer getCONSTRAINTTYPE_() {
                    return cONSTRAINTTYPE_;
                }

                public void setCONSTRAINTTYPE_(Integer cONSTRAINTTYPE_) {
                    this.cONSTRAINTTYPE_ = cONSTRAINTTYPE_;
                }

                public String getCONSTRAINTDATE_() {
                    return cONSTRAINTDATE_;
                }

                public void setCONSTRAINTDATE_(String cONSTRAINTDATE_) {
                    this.cONSTRAINTDATE_ = cONSTRAINTDATE_;
                }

                public Object getMILESTONE_() {
                    return mILESTONE_;
                }

                public void setMILESTONE_(Object mILESTONE_) {
                    this.mILESTONE_ = mILESTONE_;
                }

                public Integer getSUMMARY_() {
                    return sUMMARY_;
                }

                public void setSUMMARY_(Integer sUMMARY_) {
                    this.sUMMARY_ = sUMMARY_;
                }

                public Integer getCRITICAL_() {
                    return cRITICAL_;
                }

                public void setCRITICAL_(Integer cRITICAL_) {
                    this.cRITICAL_ = cRITICAL_;
                }

                public Object getPRIORITY_() {
                    return pRIORITY_;
                }

                public void setPRIORITY_(Object pRIORITY_) {
                    this.pRIORITY_ = pRIORITY_;
                }

                public Object getNOTES_() {
                    return nOTES_;
                }

                public void setNOTES_(Object nOTES_) {
                    this.nOTES_ = nOTES_;
                }

                public Object getDEPARTMENT_() {
                    return dEPARTMENT_;
                }

                public void setDEPARTMENT_(Object dEPARTMENT_) {
                    this.dEPARTMENT_ = dEPARTMENT_;
                }

                public Object getPRINCIPAL_() {
                    return pRINCIPAL_;
                }

                public void setPRINCIPAL_(Object pRINCIPAL_) {
                    this.pRINCIPAL_ = pRINCIPAL_;
                }

                public String getPREDECESSORLINK_() {
                    return pREDECESSORLINK_;
                }

                public void setPREDECESSORLINK_(String pREDECESSORLINK_) {
                    this.pREDECESSORLINK_ = pREDECESSORLINK_;
                }

                public Integer getFIXEDDATE_() {
                    return fIXEDDATE_;
                }

                public void setFIXEDDATE_(Integer fIXEDDATE_) {
                    this.fIXEDDATE_ = fIXEDDATE_;
                }

                public String getPARENTTASKUID_() {
                    return pARENTTASKUID_;
                }

                public void setPARENTTASKUID_(String pARENTTASKUID_) {
                    this.pARENTTASKUID_ = pARENTTASKUID_;
                }

                public String getPROJECTUID_() {
                    return pROJECTUID_;
                }

                public void setPROJECTUID_(String pROJECTUID_) {
                    this.pROJECTUID_ = pROJECTUID_;
                }

                public Object getACTUALSTART_() {
                    return aCTUALSTART_;
                }

                public void setACTUALSTART_(Object aCTUALSTART_) {
                    this.aCTUALSTART_ = aCTUALSTART_;
                }

                public Object getACTUALFINISH_() {
                    return aCTUALFINISH_;
                }

                public void setACTUALFINISH_(Object aCTUALFINISH_) {
                    this.aCTUALFINISH_ = aCTUALFINISH_;
                }

                public Object getACTUALDURATION_() {
                    return aCTUALDURATION_;
                }

                public void setACTUALDURATION_(Object aCTUALDURATION_) {
                    this.aCTUALDURATION_ = aCTUALDURATION_;
                }

                public Object getASSIGNMENTS_() {
                    return aSSIGNMENTS_;
                }

                public void setASSIGNMENTS_(Object aSSIGNMENTS_) {
                    this.aSSIGNMENTS_ = aSSIGNMENTS_;
                }

                public Object getWBS_() {
                    return wBS_;
                }

                public void setWBS_(Object wBS_) {
                    this.wBS_ = wBS_;
                }

                public Object getCRITICAL2_() {
                    return cRITICAL2_;
                }

                public void setCRITICAL2_(Object cRITICAL2_) {
                    this.cRITICAL2_ = cRITICAL2_;
                }
            }

            public List<ImgesDTO> getImges() {
                return imges;
            }

            public void setImges(List<ImgesDTO> imges) {
                this.imges = imges;
            }

            public static class ImgesDTO implements Serializable {
                @SerializedName("IssueId")
                private String issueId;
                private String bf_guid;
                private String bf_md5;
                private String bf_path;
                private String bf_filename;

                public String getBf_md5() {
                    return bf_md5;
                }

                public void setBf_md5(String bf_md5) {
                    this.bf_md5 = bf_md5;
                }

                public String getBf_path() {
                    return bf_path;
                }

                public void setBf_path(String bf_path) {
                    this.bf_path = bf_path;
                }

                public String getBf_filename() {
                    return bf_filename;
                }

                public void setBf_filename(String bf_filename) {
                    this.bf_filename = bf_filename;
                }

                public String getIssueId() {
                    return issueId;
                }

                public void setIssueId(String issueId) {
                    this.issueId = issueId;
                }

                public String getBf_guid() {
                    return bf_guid;
                }

                public void setBf_guid(String bf_guid) {
                    this.bf_guid = bf_guid;

                }
            }
        }
    }
}
