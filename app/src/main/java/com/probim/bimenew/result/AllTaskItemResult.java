package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/12/14/14:37
 */
public class AllTaskItemResult implements Serializable {

    /**
     * Ret : 1
     * Msg : OK
     * Data : {"Tasks":[{"UID_":"D95E1FD3-7E94-44C4-A0FB-4D13F5C43E21","ID_":1,"NAME_":"A-1","START_":"2020-11-09T00:00:00","FINISH_":"2020-11-13T23:59:59","DURATION_":5,"WORK_":0,"PERCENTCOMPLETE_":0,"MANUAL_":0,"WEIGHT_":0,"CONSTRAINTTYPE_":4,"CONSTRAINTDATE_":"2020-11-09T00:00:00","MILESTONE_":null,"SUMMARY_":0,"CRITICAL_":0,"PRIORITY_":null,"NOTES_":null,"DEPARTMENT_":null,"PRINCIPAL_":null,"PREDECESSORLINK_":"[]","FIXEDDATE_":0,"PARENTTASKUID_":"-1","PROJECTUID_":"acf21420-c7b6-47ea-8a6e-8f965ae2fb13","ACTUALSTART_":null,"ACTUALFINISH_":null,"ACTUALDURATION_":null,"ASSIGNMENTS_":null,"WBS_":null,"CRITICAL2_":null},{"UID_":"06022B26-2E65-4EF1-86BE-61BA64FEFA45","ID_":2,"NAME_":"A-2","START_":"2020-11-09T00:00:00","FINISH_":"2020-11-10T23:59:59","DURATION_":2,"WORK_":0,"PERCENTCOMPLETE_":0,"MANUAL_":0,"WEIGHT_":0,"CONSTRAINTTYPE_":4,"CONSTRAINTDATE_":"2020-11-09T00:00:00","MILESTONE_":null,"SUMMARY_":0,"CRITICAL_":0,"PRIORITY_":null,"NOTES_":null,"DEPARTMENT_":null,"PRINCIPAL_":null,"PREDECESSORLINK_":"[]","FIXEDDATE_":0,"PARENTTASKUID_":"-1","PROJECTUID_":"acf21420-c7b6-47ea-8a6e-8f965ae2fb13","ACTUALSTART_":null,"ACTUALFINISH_":null,"ACTUALDURATION_":null,"ASSIGNMENTS_":null,"WBS_":null,"CRITICAL2_":null},{"UID_":"909FA113-FC04-4CC2-9BBF-D1DA9A75AD89","ID_":3,"NAME_":"A-3","START_":"2020-11-13T00:00:00","FINISH_":"2020-11-23T23:59:59","DURATION_":7,"WORK_":0,"PERCENTCOMPLETE_":0,"MANUAL_":0,"WEIGHT_":0,"CONSTRAINTTYPE_":4,"CONSTRAINTDATE_":"2020-11-13T00:00:00","MILESTONE_":null,"SUMMARY_":0,"CRITICAL_":0,"PRIORITY_":null,"NOTES_":null,"DEPARTMENT_":null,"PRINCIPAL_":null,"PREDECESSORLINK_":"[]","FIXEDDATE_":0,"PARENTTASKUID_":"-1","PROJECTUID_":"acf21420-c7b6-47ea-8a6e-8f965ae2fb13","ACTUALSTART_":null,"ACTUALFINISH_":null,"ACTUALDURATION_":null,"ASSIGNMENTS_":null,"WBS_":null,"CRITICAL2_":null}]}
     */

    private int Ret;
    private String Msg;
    private DataBean Data;

    public int getRet() {
        return Ret;
    }

    public void setRet(int Ret) {
        this.Ret = Ret;
    }

    public String getMsg() {
        return Msg;
    }

    public void setMsg(String Msg) {
        this.Msg = Msg;
    }

    public DataBean getData() {
        return Data;
    }

    public void setData(DataBean Data) {
        this.Data = Data;
    }

    public static class DataBean implements Serializable {
        private List<TasksBean> Tasks;

        public List<TasksBean> getTasks() {
            return Tasks;
        }

        public void setTasks(List<TasksBean> Tasks) {
            this.Tasks = Tasks;
        }

        public static class TasksBean implements Serializable {
            /**
             * UID_ : D95E1FD3-7E94-44C4-A0FB-4D13F5C43E21
             * ID_ : 1
             * NAME_ : A-1
             * START_ : 2020-11-09T00:00:00
             * FINISH_ : 2020-11-13T23:59:59
             * DURATION_ : 5
             * WORK_ : 0
             * PERCENTCOMPLETE_ : 0
             * MANUAL_ : 0
             * WEIGHT_ : 0
             * CONSTRAINTTYPE_ : 4
             * CONSTRAINTDATE_ : 2020-11-09T00:00:00
             * MILESTONE_ : null
             * SUMMARY_ : 0
             * CRITICAL_ : 0
             * PRIORITY_ : null
             * NOTES_ : null
             * DEPARTMENT_ : null
             * PRINCIPAL_ : null
             * PREDECESSORLINK_ : []
             * FIXEDDATE_ : 0
             * PARENTTASKUID_ : -1
             * PROJECTUID_ : acf21420-c7b6-47ea-8a6e-8f965ae2fb13
             * ACTUALSTART_ : null
             * ACTUALFINISH_ : null
             * ACTUALDURATION_ : null
             * ASSIGNMENTS_ : null
             * WBS_ : null
             * CRITICAL2_ : null
             */

            private String UID_;
            private int ID_;
            private String NAME_;
            private String START_;
            private String FINISH_;
            private int DURATION_;
            private int WORK_;
            private double PERCENTCOMPLETE_;
            private int MANUAL_;
            private int WEIGHT_;
            private int CONSTRAINTTYPE_;
            private String CONSTRAINTDATE_;
            private int MILESTONE_;
            private int SUMMARY_;
            private int CRITICAL_;
            private int PRIORITY_;
            private Object NOTES_;
            private Object DEPARTMENT_;
            private Object PRINCIPAL_;
            private String PREDECESSORLINK_;
            private int FIXEDDATE_;
            private String PARENTTASKUID_;
            private String PROJECTUID_;
            private Object ACTUALSTART_;
            private Object ACTUALFINISH_;
            private int ACTUALDURATION_;
            private Object ASSIGNMENTS_;
            private String WBS_;
            private int CRITICAL2_;
            private boolean isSelected;
            private List<TasksBean> children;

            public List<TasksBean> getChildren() {
                return children;
            }

            public void setChildren(List<TasksBean> children) {
                this.children = children;
            }

            public boolean isSelected() {
                return isSelected;
            }

            public void setSelected(boolean selected) {
                isSelected = selected;
            }

            public String getUID_() {
                return UID_;
            }

            public void setUID_(String UID_) {
                this.UID_ = UID_;
            }

            public int getID_() {
                return ID_;
            }

            public void setID_(int ID_) {
                this.ID_ = ID_;
            }

            public String getNAME_() {
                return NAME_;
            }

            public void setNAME_(String NAME_) {
                this.NAME_ = NAME_;
            }

            public String getSTART_() {
                return START_;
            }

            public void setSTART_(String START_) {
                this.START_ = START_;
            }

            public String getFINISH_() {
                return FINISH_;
            }

            public void setFINISH_(String FINISH_) {
                this.FINISH_ = FINISH_;
            }

            public int getDURATION_() {
                return DURATION_;
            }

            public void setDURATION_(int DURATION_) {
                this.DURATION_ = DURATION_;
            }

            public int getWORK_() {
                return WORK_;
            }

            public void setWORK_(int WORK_) {
                this.WORK_ = WORK_;
            }

            public double getPERCENTCOMPLETE_() {
                return PERCENTCOMPLETE_;
            }

            public void setPERCENTCOMPLETE_(double PERCENTCOMPLETE_) {
                this.PERCENTCOMPLETE_ = PERCENTCOMPLETE_;
            }

            public int getMANUAL_() {
                return MANUAL_;
            }

            public void setMANUAL_(int MANUAL_) {
                this.MANUAL_ = MANUAL_;
            }

            public int getWEIGHT_() {
                return WEIGHT_;
            }

            public void setWEIGHT_(int WEIGHT_) {
                this.WEIGHT_ = WEIGHT_;
            }

            public int getCONSTRAINTTYPE_() {
                return CONSTRAINTTYPE_;
            }

            public void setCONSTRAINTTYPE_(int CONSTRAINTTYPE_) {
                this.CONSTRAINTTYPE_ = CONSTRAINTTYPE_;
            }

            public String getCONSTRAINTDATE_() {
                return CONSTRAINTDATE_;
            }

            public void setCONSTRAINTDATE_(String CONSTRAINTDATE_) {
                this.CONSTRAINTDATE_ = CONSTRAINTDATE_;
            }

            public int getMILESTONE_() {
                return MILESTONE_;
            }

            public void setMILESTONE_(int MILESTONE_) {
                this.MILESTONE_ = MILESTONE_;
            }

            public int getSUMMARY_() {
                return SUMMARY_;
            }

            public void setSUMMARY_(int SUMMARY_) {
                this.SUMMARY_ = SUMMARY_;
            }

            public int getCRITICAL_() {
                return CRITICAL_;
            }

            public void setCRITICAL_(int CRITICAL_) {
                this.CRITICAL_ = CRITICAL_;
            }

            public int getPRIORITY_() {
                return PRIORITY_;
            }

            public void setPRIORITY_(int PRIORITY_) {
                this.PRIORITY_ = PRIORITY_;
            }

            public Object getNOTES_() {
                return NOTES_;
            }

            public void setNOTES_(Object NOTES_) {
                this.NOTES_ = NOTES_;
            }

            public Object getDEPARTMENT_() {
                return DEPARTMENT_;
            }

            public void setDEPARTMENT_(Object DEPARTMENT_) {
                this.DEPARTMENT_ = DEPARTMENT_;
            }

            public Object getPRINCIPAL_() {
                return PRINCIPAL_;
            }

            public void setPRINCIPAL_(Object PRINCIPAL_) {
                this.PRINCIPAL_ = PRINCIPAL_;
            }

            public String getPREDECESSORLINK_() {
                return PREDECESSORLINK_;
            }

            public void setPREDECESSORLINK_(String PREDECESSORLINK_) {
                this.PREDECESSORLINK_ = PREDECESSORLINK_;
            }

            public int getFIXEDDATE_() {
                return FIXEDDATE_;
            }

            public void setFIXEDDATE_(int FIXEDDATE_) {
                this.FIXEDDATE_ = FIXEDDATE_;
            }

            public String getPARENTTASKUID_() {
                return PARENTTASKUID_;
            }

            public void setPARENTTASKUID_(String PARENTTASKUID_) {
                this.PARENTTASKUID_ = PARENTTASKUID_;
            }

            public String getPROJECTUID_() {
                return PROJECTUID_;
            }

            public void setPROJECTUID_(String PROJECTUID_) {
                this.PROJECTUID_ = PROJECTUID_;
            }

            public Object getACTUALSTART_() {
                return ACTUALSTART_;
            }

            public void setACTUALSTART_(Object ACTUALSTART_) {
                this.ACTUALSTART_ = ACTUALSTART_;
            }

            public Object getACTUALFINISH_() {
                return ACTUALFINISH_;
            }

            public void setACTUALFINISH_(Object ACTUALFINISH_) {
                this.ACTUALFINISH_ = ACTUALFINISH_;
            }

            public int getACTUALDURATION_() {
                return ACTUALDURATION_;
            }

            public void setACTUALDURATION_(int ACTUALDURATION_) {
                this.ACTUALDURATION_ = ACTUALDURATION_;
            }

            public Object getASSIGNMENTS_() {
                return ASSIGNMENTS_;
            }

            public void setASSIGNMENTS_(Object ASSIGNMENTS_) {
                this.ASSIGNMENTS_ = ASSIGNMENTS_;
            }


            public int getCRITICAL2_() {
                return CRITICAL2_;
            }

            public void setCRITICAL2_(int CRITICAL2_) {
                this.CRITICAL2_ = CRITICAL2_;
            }

            public String getWBS_() {
                return WBS_;
            }

            public void setWBS_(String WBS_) {
                this.WBS_ = WBS_;
            }
        }
    }
}
