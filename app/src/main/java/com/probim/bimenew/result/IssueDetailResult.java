package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

public class IssueDetailResult implements Serializable {

  /**
   * Ret : 1
   * Msg : OK
   * Data : {"activities":[{"RealName":"贾磊","ol_guid":"e3232b73-1eff-4cf2-a4f1-15eeed150421","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:21","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"移除了参与人
   * \u201c王平\u201d","ol_type":"移除了参与人 \u201c王平\u201d"},{"RealName":"贾磊","ol_guid":"e7dc574a-54c8-48f0-b076-a8d678810f71","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:15","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"修改了问题类型为
   * \u201c质量\u201d","ol_type":"修改了问题类型为 \u201c质量\u201d"},{"RealName":"贾磊","ol_guid":"416456c2-a3f3-4dee-a964-7e563e549f89","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:08","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"修改了问题状态为
   * \u201c已完成\u201d","ol_type":"修改了问题状态为 \u201c已完成\u201d"},{"RealName":"贾磊","ol_guid":"04ff49af-5c57-4461-84a7-19ca3caffae8","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:46:49","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"发起了该问题","ol_type":"发起了该问题"}],"isIssueManager":true,"issueObj":{"base_files":null,"IssueTypeText":"质量","IssueStatusText":"已完成","FileId":null,"IsHide":false,"FileCount":0,"TalkCount":0,"bdid_status_SortCode":null,"IssueStatusColor":null,"bu_Email":null,"bu_Mobile":null,"RelationFiles":null,"bgpicture_src":null,"com_FullName":null,"TagIdList":null,"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ProjectID":"fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5","ModelId":"37b36696-e40b-47b3-9c3e-08db0cdc08e6","ViewpointID":"{\"hidedElement\":[],\"isolateElelement\":[],\"clipInof\":null,\"cameraInof\":{\"position\":{\"x\":114.81652969762686,\"y\":34.411152597643806,\"z\":-8.247095192760245},\"rotation\":{\"_x\":-2.2067233348558175,\"_y\":1.************,\"_z\":2.232578646673958,\"_order\":\"XYZ\"},\"target\":{\"x\":-0.4629497528076172,\"y\":3.1799230575561523,\"z\":14.80880069732666}}}","Title":"      
   * 测试专用(勿删)","ImageUrl":"https://www.probim.cn:8080/ModelImages/2020/2/26/47a170b5-349e-46af-9ec7-c735585a04c0.bmp","Content":"      
   * 测试专用(勿删)","IssueStatus":"b28afe81-2e4f-4137-9291-ff6eb0049df5","CreateDate":"2020-02-26T14:46:48","CreateUserId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","CreateUserName":"贾磊","EndDate":"2020-05-16T00:00:00","IsPublic":true,"IssueTypeID":"9744e84c-a1c0-460f-9ba0-ab88719931f0","DeleteMark":0},"modelname":"rac_basic_sample_project","relationFiles":[{"FileId":"9b77b9c8-34f1-45f2-86c2-89e8f56b80da","FolderId":"","FileName":"测试专用
   * (勿删).docx","FilePath":"","FileSize":"11943","FileExtensions":".docx","FileType":"Hide","IsShare":0,"ShareLink":"","ShareCode":null,"ShareTime":null,"DownloadCount":0,"IsTop":0,"SortCode":-1,"DeleteMark":0,"EnabledMark":1,"Description":"","CreateDate":"2020-02-26T14:46:42","CreateUserId":"","CreateUserName":"","ModifyDate":"2020-02-26T14:46:42","ModifyUserId":"","ModifyUserName":""}],"base_files":[{"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","bf_guid":"ca57ce43-ca61-434f-8169-6d14af841ade","bf_md5":"a4bbe7753b6b64eb2a9bda87eb59e817","bf_path":"/IssueImages/2020/2/21/c2fb0ab4-b270-46c5-88c3-3903eac9b873.jpg","bf_filename":"testImg.jpg"},{"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","bf_guid":"714c6f8b-9eb8-4e3e-a2c2-52406f01e4eb","bf_md5":"5c07c7fbb5a02c3b6b40095b01644513","bf_path":"/IssueImages/2020/2/26/9e143346-d2e6-4466-85ff-eed2c300021a.jpg","bf_filename":"v2-793b47c44284538aa44757bd52e5c3b4_b.jpg"}],"comments":[{"contenttypeJson":{"type":"image","contentImgId":"390b5fa8-50d0-46f2-afc6-0c478da35ea2"},"issue_talkid":"9ce98a9f-ec3d-4275-ac8d-a9f0b6cf619c","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"testImg.jpg","createdate":"2020/2/26
   * 16:48:15","contenttype":"{\"type\":\"image\",\"id\":\"390b5fa8-50d0-46f2-afc6-0c478da35ea2\"}","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"text","contentImgId":""},"issue_talkid":"ff48f889-dc59-4aa6-bb2d-bfabf31e4bb9","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"sssssssd","createdate":"2020/2/26
   * 16:32:27","contenttype":"text","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"text","contentImgId":""},"issue_talkid":"5347ae00-6fba-464d-8b64-5436234af47a","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"数据1","createdate":"2020/2/26
   * 16:16:19","contenttype":"text","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"image","contentImgId":"3e9ab2cc-3dcc-4bc4-9fa7-be440690415e"},"issue_talkid":"ba0193c7-98be-48b6-82ea-13e2ecdc5312","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"testImg.jpg","createdate":"2020/2/26
   * 14:49:11","contenttype":"{\"type\":\"image\",\"id\":\"3e9ab2cc-3dcc-4bc4-9fa7-be440690415e\"}","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]}],"joiners":[{"UserId":"7fc8eb07-da6e-4912-b144-7a9247020f5a","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"王子文","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"UserId":"511a142b-7cae-4a13-a0cb-fa063a4be315","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"陈亚博","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"UserId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"贾磊","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}],"tagList":[]}
   */

  private int Ret;
  private String Msg;
  private DataBean Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public DataBean getData() {
    return Data;
  }

  public void setData(DataBean Data) {
    this.Data = Data;
  }

  public static class DataBean implements Serializable{
    /**
     * activities : [{"RealName":"贾磊","ol_guid":"e3232b73-1eff-4cf2-a4f1-15eeed150421","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:21","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"移除了参与人
     * \u201c王平\u201d","ol_type":"移除了参与人 \u201c王平\u201d"},{"RealName":"贾磊","ol_guid":"e7dc574a-54c8-48f0-b076-a8d678810f71","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:15","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"修改了问题类型为
     * \u201c质量\u201d","ol_type":"修改了问题类型为 \u201c质量\u201d"},{"RealName":"贾磊","ol_guid":"416456c2-a3f3-4dee-a964-7e563e549f89","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:48:08","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"修改了问题状态为
     * \u201c已完成\u201d","ol_type":"修改了问题状态为 \u201c已完成\u201d"},{"RealName":"贾磊","ol_guid":"04ff49af-5c57-4461-84a7-19ca3caffae8","ol_objId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ol_datetime":"2020-02-26T14:46:49","ol_operId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","ol_content":"发起了该问题","ol_type":"发起了该问题"}]
     * isIssueManager : true
     * issueObj : {"base_files":null,"IssueTypeText":"质量","IssueStatusText":"已完成","FileId":null,"IsHide":false,"FileCount":0,"TalkCount":0,"bdid_status_SortCode":null,"IssueStatusColor":null,"bu_Email":null,"bu_Mobile":null,"RelationFiles":null,"bgpicture_src":null,"com_FullName":null,"TagIdList":null,"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","ProjectID":"fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5","ModelId":"37b36696-e40b-47b3-9c3e-08db0cdc08e6","ViewpointID":"{\"hidedElement\":[],\"isolateElelement\":[],\"clipInof\":null,\"cameraInof\":{\"position\":{\"x\":114.81652969762686,\"y\":34.411152597643806,\"z\":-8.247095192760245},\"rotation\":{\"_x\":-2.2067233348558175,\"_y\":1.************,\"_z\":2.232578646673958,\"_order\":\"XYZ\"},\"target\":{\"x\":-0.4629497528076172,\"y\":3.1799230575561523,\"z\":14.80880069732666}}}","Title":"      
     * 测试专用(勿删)","ImageUrl":"https://www.probim.cn:8080/ModelImages/2020/2/26/47a170b5-349e-46af-9ec7-c735585a04c0.bmp","Content":"      
     * 测试专用(勿删)","IssueStatus":"b28afe81-2e4f-4137-9291-ff6eb0049df5","CreateDate":"2020-02-26T14:46:48","CreateUserId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","CreateUserName":"贾磊","EndDate":"2020-05-16T00:00:00","IsPublic":true,"IssueTypeID":"9744e84c-a1c0-460f-9ba0-ab88719931f0","DeleteMark":0}
     * modelname : rac_basic_sample_project
     * relationFiles : [{"FileId":"9b77b9c8-34f1-45f2-86c2-89e8f56b80da","FolderId":"","FileName":"测试专用
     * (勿删).docx","FilePath":"","FileSize":"11943","FileExtensions":".docx","FileType":"Hide","IsShare":0,"ShareLink":"","ShareCode":null,"ShareTime":null,"DownloadCount":0,"IsTop":0,"SortCode":-1,"DeleteMark":0,"EnabledMark":1,"Description":"","CreateDate":"2020-02-26T14:46:42","CreateUserId":"","CreateUserName":"","ModifyDate":"2020-02-26T14:46:42","ModifyUserId":"","ModifyUserName":""}]
     * base_files : [{"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","bf_guid":"ca57ce43-ca61-434f-8169-6d14af841ade","bf_md5":"a4bbe7753b6b64eb2a9bda87eb59e817","bf_path":"/IssueImages/2020/2/21/c2fb0ab4-b270-46c5-88c3-3903eac9b873.jpg","bf_filename":"testImg.jpg"},{"IssueId":"2aa25cda-f990-42cd-826d-e91d90ee859e","bf_guid":"714c6f8b-9eb8-4e3e-a2c2-52406f01e4eb","bf_md5":"5c07c7fbb5a02c3b6b40095b01644513","bf_path":"/IssueImages/2020/2/26/9e143346-d2e6-4466-85ff-eed2c300021a.jpg","bf_filename":"v2-793b47c44284538aa44757bd52e5c3b4_b.jpg"}]
     * comments : [{"contenttypeJson":{"type":"image","contentImgId":"390b5fa8-50d0-46f2-afc6-0c478da35ea2"},"issue_talkid":"9ce98a9f-ec3d-4275-ac8d-a9f0b6cf619c","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"testImg.jpg","createdate":"2020/2/26
     * 16:48:15","contenttype":"{\"type\":\"image\",\"id\":\"390b5fa8-50d0-46f2-afc6-0c478da35ea2\"}","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"text","contentImgId":""},"issue_talkid":"ff48f889-dc59-4aa6-bb2d-bfabf31e4bb9","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"sssssssd","createdate":"2020/2/26
     * 16:32:27","contenttype":"text","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"text","contentImgId":""},"issue_talkid":"5347ae00-6fba-464d-8b64-5436234af47a","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"数据1","createdate":"2020/2/26
     * 16:16:19","contenttype":"text","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]},{"contenttypeJson":{"type":"image","contentImgId":"3e9ab2cc-3dcc-4bc4-9fa7-be440690415e"},"issue_talkid":"ba0193c7-98be-48b6-82ea-13e2ecdc5312","issueid":"2aa25cda-f990-42cd-826d-e91d90ee859e","userid":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","content":"testImg.jpg","createdate":"2020/2/26
     * 14:49:11","contenttype":"{\"type\":\"image\",\"id\":\"3e9ab2cc-3dcc-4bc4-9fa7-be440690415e\"}","deletemark":0,"userofunread":"","issue_ptalkid":"","realname":"贾磊","targetids":"","innerlist":[]}]
     * joiners : [{"UserId":"7fc8eb07-da6e-4912-b144-7a9247020f5a","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"王子文","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"UserId":"511a142b-7cae-4a13-a0cb-fa063a4be315","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"陈亚博","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null},{"UserId":"9077ee94-ac8c-4363-81ac-dc4d4970b4f6","EnCode":null,"Account":null,"Password":null,"Secretkey":null,"RealName":"贾磊","NickName":null,"HeadIcon":null,"QuickQuery":null,"SimpleSpelling":null,"Gender":null,"Birthday":null,"Mobile":"","Telephone":null,"Email":"","OICQ":null,"WeChat":null,"MSN":null,"ManagerId":null,"Manager":null,"OrganizeId":null,"DepartmentId":null,"RoleId":null,"DutyId":null,"DutyName":null,"PostId":null,"PostName":null,"WorkGroupId":null,"SecurityLevel":null,"UserOnLine":null,"OpenId":null,"Question":null,"AnswerQuestion":null,"CheckOnLine":null,"AllowStartTime":null,"AllowEndTime":null,"LockStartDate":null,"LockEndDate":null,"FirstVisit":null,"PreviousVisit":null,"LastVisit":null,"LogOnCount":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"UserType":null,"DataFrom":null}]
     * tagList : []
     */

    private boolean isIssueManager;
    private IssueObjBean issueObj;
    private String modelname;
    private List<ActivitiesBean> activities;
    private List<RelationFilesBean> relationFiles;
    private List<BaseFilesBean> base_files;
    private List<CommentsBean> comments;
    private List<JoinersBean> joiners;
    private List<TagListBean> tagList;

    public boolean isIsIssueManager() {
      return isIssueManager;
    }

    public void setIsIssueManager(boolean isIssueManager) {
      this.isIssueManager = isIssueManager;
    }

    public IssueObjBean getIssueObj() {
      return issueObj;
    }

    public void setIssueObj(IssueObjBean issueObj) {
      this.issueObj = issueObj;
    }

    public String getModelname() {
      return modelname;
    }

    public void setModelname(String modelname) {
      this.modelname = modelname;
    }

    public List<ActivitiesBean> getActivities() {
      return activities;
    }

    public void setActivities(List<ActivitiesBean> activities) {
      this.activities = activities;
    }

    public List<RelationFilesBean> getRelationFiles() {
      return relationFiles;
    }

    public void setRelationFiles(List<RelationFilesBean> relationFiles) {
      this.relationFiles = relationFiles;
    }

    public List<BaseFilesBean> getBase_files() {
      return base_files;
    }

    public void setBase_files(List<BaseFilesBean> base_files) {
      this.base_files = base_files;
    }

    public List<CommentsBean> getComments() {
      return comments;
    }

    public void setComments(List<CommentsBean> comments) {
      this.comments = comments;
    }

    public List<JoinersBean> getJoiners() {
      return joiners;
    }

    public void setJoiners(List<JoinersBean> joiners) {
      this.joiners = joiners;
    }

    public List<TagListBean> getTagList() {
      return tagList;
    }

    public static class IssueObjBean implements Serializable{
      /**
       * base_files : null
       * IssueTypeText : 质量
       * IssueStatusText : 已完成
       * FileId : null
       * IsHide : false
       * FileCount : 0
       * TalkCount : 0
       * bdid_status_SortCode : null
       * IssueStatusColor : null
       * bu_Email : null
       * bu_Mobile : null
       * RelationFiles : null
       * bgpicture_src : null
       * com_FullName : null
       * TagIdList : null
       * IssueId : 2aa25cda-f990-42cd-826d-e91d90ee859e
       * ProjectID : fbd2e7f7-7fc8-4f75-bb62-a988b53bb7a5
       * ModelId : 37b36696-e40b-47b3-9c3e-08db0cdc08e6
       * ViewpointID : {"hidedElement":[],"isolateElelement":[],"clipInof":null,"cameraInof":{"position":{"x":114.81652969762686,"y":34.411152597643806,"z":-8.247095192760245},"rotation":{"_x":-2.2067233348558175,"_y":1.************,"_z":2.232578646673958,"_order":"XYZ"},"target":{"x":-0.4629497528076172,"y":3.1799230575561523,"z":14.80880069732666}}}
       * Title :        测试专用(勿删)
       * ImageUrl : https://www.probim.cn:8080/ModelImages/2020/2/26/47a170b5-349e-46af-9ec7-c735585a04c0.bmp
       * Content :        测试专用(勿删)
       * IssueStatus : b28afe81-2e4f-4137-9291-ff6eb0049df5
       * CreateDate : 2020-02-26T14:46:48
       * CreateUserId : 9077ee94-ac8c-4363-81ac-dc4d4970b4f6
       * CreateUserName : 贾磊
       * EndDate : 2020-05-16T00:00:00
       * IsPublic : true
       * IssueTypeID : 9744e84c-a1c0-460f-9ba0-ab88719931f0
       * DeleteMark : 0
       */

      private Object base_files;
      private String IssueTypeText;
      private String IssueStatusText;
      private Object FileId;
      private boolean IsHide;
      private int FileCount;
      private int TalkCount;
      private Object bdid_status_SortCode;
      private Object IssueStatusColor;
      private Object bu_Email;
      private Object bu_Mobile;
      private Object RelationFiles;
      private Object bgpicture_src;
      private Object com_FullName;
      private Object TagIdList;
      private String IssueId;
      private String ProjectID;
      private String ModelId;
      private String ViewpointID;
      private String Title;
      private String ImageUrl;
      private String Content;
      private String IssueStatus;
      private String CreateDate;
      private String CreateUserId;
      private String CreateUserName;
      private String EndDate;
      private boolean IsPublic;
      private String IssueTypeID;
      private int DeleteMark;

      public Object getBase_files() {
        return base_files;
      }

      public void setBase_files(Object base_files) {
        this.base_files = base_files;
      }

      public String getIssueTypeText() {
        return IssueTypeText;
      }

      public void setIssueTypeText(String IssueTypeText) {
        this.IssueTypeText = IssueTypeText;
      }

      public String getIssueStatusText() {
        return IssueStatusText;
      }

      public void setIssueStatusText(String IssueStatusText) {
        this.IssueStatusText = IssueStatusText;
      }

      public Object getFileId() {
        return FileId;
      }

      public void setFileId(Object FileId) {
        this.FileId = FileId;
      }

      public boolean isIsHide() {
        return IsHide;
      }

      public void setIsHide(boolean IsHide) {
        this.IsHide = IsHide;
      }

      public int getFileCount() {
        return FileCount;
      }

      public void setFileCount(int FileCount) {
        this.FileCount = FileCount;
      }

      public int getTalkCount() {
        return TalkCount;
      }

      public void setTalkCount(int TalkCount) {
        this.TalkCount = TalkCount;
      }

      public Object getBdid_status_SortCode() {
        return bdid_status_SortCode;
      }

      public void setBdid_status_SortCode(Object bdid_status_SortCode) {
        this.bdid_status_SortCode = bdid_status_SortCode;
      }

      public Object getIssueStatusColor() {
        return IssueStatusColor;
      }

      public void setIssueStatusColor(Object IssueStatusColor) {
        this.IssueStatusColor = IssueStatusColor;
      }

      public Object getBu_Email() {
        return bu_Email;
      }

      public void setBu_Email(Object bu_Email) {
        this.bu_Email = bu_Email;
      }

      public Object getBu_Mobile() {
        return bu_Mobile;
      }

      public void setBu_Mobile(Object bu_Mobile) {
        this.bu_Mobile = bu_Mobile;
      }

      public Object getRelationFiles() {
        return RelationFiles;
      }

      public void setRelationFiles(Object RelationFiles) {
        this.RelationFiles = RelationFiles;
      }

      public Object getBgpicture_src() {
        return bgpicture_src;
      }

      public void setBgpicture_src(Object bgpicture_src) {
        this.bgpicture_src = bgpicture_src;
      }

      public Object getCom_FullName() {
        return com_FullName;
      }

      public void setCom_FullName(Object com_FullName) {
        this.com_FullName = com_FullName;
      }

      public Object getTagIdList() {
        return TagIdList;
      }

      public void setTagIdList(Object TagIdList) {
        this.TagIdList = TagIdList;
      }

      public String getIssueId() {
        return IssueId;
      }

      public void setIssueId(String IssueId) {
        this.IssueId = IssueId;
      }

      public String getProjectID() {
        return ProjectID;
      }

      public void setProjectID(String ProjectID) {
        this.ProjectID = ProjectID;
      }

      public String getModelId() {
        return ModelId;
      }

      public void setModelId(String ModelId) {
        this.ModelId = ModelId;
      }

      public String getViewpointID() {
        return ViewpointID;
      }

      public void setViewpointID(String ViewpointID) {
        this.ViewpointID = ViewpointID;
      }

      public String getTitle() {
        return Title;
      }

      public void setTitle(String Title) {
        this.Title = Title;
      }

      public String getImageUrl() {
        return ImageUrl;
      }

      public void setImageUrl(String ImageUrl) {
        this.ImageUrl = ImageUrl;
      }

      public String getContent() {
        return Content;
      }

      public void setContent(String Content) {
        this.Content = Content;
      }

      public String getIssueStatus() {
        return IssueStatus;
      }

      public void setIssueStatus(String IssueStatus) {
        this.IssueStatus = IssueStatus;
      }

      public String getCreateDate() {
        return CreateDate;
      }

      public void setCreateDate(String CreateDate) {
        this.CreateDate = CreateDate;
      }

      public String getCreateUserId() {
        return CreateUserId;
      }

      public void setCreateUserId(String CreateUserId) {
        this.CreateUserId = CreateUserId;
      }

      public String getCreateUserName() {
        return CreateUserName;
      }

      public void setCreateUserName(String CreateUserName) {
        this.CreateUserName = CreateUserName;
      }

      public String getEndDate() {
        return EndDate;
      }

      public void setEndDate(String EndDate) {
        this.EndDate = EndDate;
      }

      public boolean isIsPublic() {
        return IsPublic;
      }

      public void setIsPublic(boolean IsPublic) {
        this.IsPublic = IsPublic;
      }

      public String getIssueTypeID() {
        return IssueTypeID;
      }

      public void setIssueTypeID(String IssueTypeID) {
        this.IssueTypeID = IssueTypeID;
      }

      public int getDeleteMark() {
        return DeleteMark;
      }

      public void setDeleteMark(int DeleteMark) {
        this.DeleteMark = DeleteMark;
      }
    }

    public static class ActivitiesBean implements Serializable{
      /**
       * RealName : 贾磊
       * ol_guid : e3232b73-1eff-4cf2-a4f1-15eeed150421
       * ol_objId : 2aa25cda-f990-42cd-826d-e91d90ee859e
       * ol_datetime : 2020-02-26T14:48:21
       * ol_operId : 9077ee94-ac8c-4363-81ac-dc4d4970b4f6
       * ol_content : 移除了参与人 “王平”
       * ol_type : 移除了参与人 “王平”
       */

      private String RealName;
      private String ol_guid;
      private String ol_objId;
      private String ol_datetime;
      private String ol_operId;
      private String ol_content;
      private String ol_type;

      public String getRealName() {
        return RealName;
      }

      public void setRealName(String RealName) {
        this.RealName = RealName;
      }

      public String getOl_guid() {
        return ol_guid;
      }

      public void setOl_guid(String ol_guid) {
        this.ol_guid = ol_guid;
      }

      public String getOl_objId() {
        return ol_objId;
      }

      public void setOl_objId(String ol_objId) {
        this.ol_objId = ol_objId;
      }

      public String getOl_datetime() {
        return ol_datetime;
      }

      public void setOl_datetime(String ol_datetime) {
        this.ol_datetime = ol_datetime;
      }

      public String getOl_operId() {
        return ol_operId;
      }

      public void setOl_operId(String ol_operId) {
        this.ol_operId = ol_operId;
      }

      public String getOl_content() {
        return ol_content;
      }

      public void setOl_content(String ol_content) {
        this.ol_content = ol_content;
      }

      public String getOl_type() {
        return ol_type;
      }

      public void setOl_type(String ol_type) {
        this.ol_type = ol_type;
      }
    }

    public static class RelationFilesBean implements Serializable{
      /**
       * FileId : 9b77b9c8-34f1-45f2-86c2-89e8f56b80da
       * FolderId :
       * FileName : 测试专用 (勿删).docx
       * FilePath :
       * FileSize : 11943
       * FileExtensions : .docx
       * FileType : Hide
       * IsShare : 0
       * ShareLink :
       * ShareCode : null
       * ShareTime : null
       * DownloadCount : 0
       * IsTop : 0
       * SortCode : -1
       * DeleteMark : 0
       * EnabledMark : 1
       * Description :
       * CreateDate : 2020-02-26T14:46:42
       * CreateUserId :
       * CreateUserName :
       * ModifyDate : 2020-02-26T14:46:42
       * ModifyUserId :
       * ModifyUserName :
       */

      private String FileId;
      private String FolderId;
      private String FileName;
      private String FilePath;
      private String FileSize;
      private String FileExtensions;
      private String FileType;
      private int IsShare;
      private String ShareLink;
      private Object ShareCode;
      private Object ShareTime;
      private int DownloadCount;
      private int IsTop;
      private int SortCode;
      private int DeleteMark;
      private int EnabledMark;
      private String Description;
      private String CreateDate;
      private String CreateUserId;
      private String CreateUserName;
      private String ModifyDate;
      private String ModifyUserId;
      private String ModifyUserName;

      public String getFileId() {
        return FileId;
      }

      public void setFileId(String FileId) {
        this.FileId = FileId;
      }

      public String getFolderId() {
        return FolderId;
      }

      public void setFolderId(String FolderId) {
        this.FolderId = FolderId;
      }

      public String getFileName() {
        return FileName;
      }

      public void setFileName(String FileName) {
        this.FileName = FileName;
      }

      public String getFilePath() {
        return FilePath;
      }

      public void setFilePath(String FilePath) {
        this.FilePath = FilePath;
      }

      public String getFileSize() {
        return FileSize;
      }

      public void setFileSize(String FileSize) {
        this.FileSize = FileSize;
      }

      public String getFileExtensions() {
        return FileExtensions;
      }

      public void setFileExtensions(String FileExtensions) {
        this.FileExtensions = FileExtensions;
      }

      public String getFileType() {
        return FileType;
      }

      public void setFileType(String FileType) {
        this.FileType = FileType;
      }

      public int getIsShare() {
        return IsShare;
      }

      public void setIsShare(int IsShare) {
        this.IsShare = IsShare;
      }

      public String getShareLink() {
        return ShareLink;
      }

      public void setShareLink(String ShareLink) {
        this.ShareLink = ShareLink;
      }

      public Object getShareCode() {
        return ShareCode;
      }

      public void setShareCode(Object ShareCode) {
        this.ShareCode = ShareCode;
      }

      public Object getShareTime() {
        return ShareTime;
      }

      public void setShareTime(Object ShareTime) {
        this.ShareTime = ShareTime;
      }

      public int getDownloadCount() {
        return DownloadCount;
      }

      public void setDownloadCount(int DownloadCount) {
        this.DownloadCount = DownloadCount;
      }

      public int getIsTop() {
        return IsTop;
      }

      public void setIsTop(int IsTop) {
        this.IsTop = IsTop;
      }

      public int getSortCode() {
        return SortCode;
      }

      public void setSortCode(int SortCode) {
        this.SortCode = SortCode;
      }

      public int getDeleteMark() {
        return DeleteMark;
      }

      public void setDeleteMark(int DeleteMark) {
        this.DeleteMark = DeleteMark;
      }

      public int getEnabledMark() {
        return EnabledMark;
      }

      public void setEnabledMark(int EnabledMark) {
        this.EnabledMark = EnabledMark;
      }

      public String getDescription() {
        return Description;
      }

      public void setDescription(String Description) {
        this.Description = Description;
      }

      public String getCreateDate() {
        return CreateDate;
      }

      public void setCreateDate(String CreateDate) {
        this.CreateDate = CreateDate;
      }

      public String getCreateUserId() {
        return CreateUserId;
      }

      public void setCreateUserId(String CreateUserId) {
        this.CreateUserId = CreateUserId;
      }

      public String getCreateUserName() {
        return CreateUserName;
      }

      public void setCreateUserName(String CreateUserName) {
        this.CreateUserName = CreateUserName;
      }

      public String getModifyDate() {
        return ModifyDate;
      }

      public void setModifyDate(String ModifyDate) {
        this.ModifyDate = ModifyDate;
      }

      public String getModifyUserId() {
        return ModifyUserId;
      }

      public void setModifyUserId(String ModifyUserId) {
        this.ModifyUserId = ModifyUserId;
      }

      public String getModifyUserName() {
        return ModifyUserName;
      }

      public void setModifyUserName(String ModifyUserName) {
        this.ModifyUserName = ModifyUserName;
      }
    }

    public static class BaseFilesBean implements Serializable{
      /**
       * IssueId : 2aa25cda-f990-42cd-826d-e91d90ee859e
       * bf_guid : ca57ce43-ca61-434f-8169-6d14af841ade
       * bf_md5 : a4bbe7753b6b64eb2a9bda87eb59e817
       * bf_path : /IssueImages/2020/2/21/c2fb0ab4-b270-46c5-88c3-3903eac9b873.jpg
       * bf_filename : testImg.jpg
       */

      private String IssueId;
      private String bf_guid;
      private String bf_md5;
      private String bf_path;
      private String bf_filename;

      public String getIssueId() {
        return IssueId;
      }

      public void setIssueId(String IssueId) {
        this.IssueId = IssueId;
      }

      public String getBf_guid() {
        return bf_guid;
      }

      public void setBf_guid(String bf_guid) {
        this.bf_guid = bf_guid;
      }

      public String getBf_md5() {
        return bf_md5;
      }

      public void setBf_md5(String bf_md5) {
        this.bf_md5 = bf_md5;
      }

      public String getBf_path() {
        return bf_path;
      }

      public void setBf_path(String bf_path) {
        this.bf_path = bf_path;
      }

      public String getBf_filename() {
        return bf_filename;
      }

      public void setBf_filename(String bf_filename) {
        this.bf_filename = bf_filename;
      }
    }

    public static class CommentsBean implements Serializable{
      /**
       * contenttypeJson : {"type":"image","contentImgId":"390b5fa8-50d0-46f2-afc6-0c478da35ea2"}
       * issue_talkid : 9ce98a9f-ec3d-4275-ac8d-a9f0b6cf619c
       * issueid : 2aa25cda-f990-42cd-826d-e91d90ee859e
       * userid : 9077ee94-ac8c-4363-81ac-dc4d4970b4f6
       * content : testImg.jpg
       * createdate : 2020/2/26 16:48:15
       * contenttype : {"type":"image","id":"390b5fa8-50d0-46f2-afc6-0c478da35ea2"}
       * deletemark : 0
       * userofunread :
       * issue_ptalkid :
       * realname : 贾磊
       * targetids :
       * innerlist : []
       */

      private ContenttypeJsonBean contenttypeJson;
      private String issue_talkid;
      private String issueid;
      private String userid;
      private String content;
      private String createdate;
      private String contenttype;
      private int deletemark;
      private String userofunread;
      private String issue_ptalkid;
      private String realname;
      private String targetids;
      private List<?> innerlist;

      public ContenttypeJsonBean getContenttypeJson() {
        return contenttypeJson;
      }

      public void setContenttypeJson(ContenttypeJsonBean contenttypeJson) {
        this.contenttypeJson = contenttypeJson;
      }

      public String getIssue_talkid() {
        return issue_talkid;
      }

      public void setIssue_talkid(String issue_talkid) {
        this.issue_talkid = issue_talkid;
      }

      public String getIssueid() {
        return issueid;
      }

      public void setIssueid(String issueid) {
        this.issueid = issueid;
      }

      public String getUserid() {
        return userid;
      }

      public void setUserid(String userid) {
        this.userid = userid;
      }

      public String getContent() {
        return content;
      }

      public void setContent(String content) {
        this.content = content;
      }

      public String getCreatedate() {
        return createdate;
      }

      public void setCreatedate(String createdate) {
        this.createdate = createdate;
      }

      public String getContenttype() {
        return contenttype;
      }

      public void setContenttype(String contenttype) {
        this.contenttype = contenttype;
      }

      public int getDeletemark() {
        return deletemark;
      }

      public void setDeletemark(int deletemark) {
        this.deletemark = deletemark;
      }

      public String getUserofunread() {
        return userofunread;
      }

      public void setUserofunread(String userofunread) {
        this.userofunread = userofunread;
      }

      public String getIssue_ptalkid() {
        return issue_ptalkid;
      }

      public void setIssue_ptalkid(String issue_ptalkid) {
        this.issue_ptalkid = issue_ptalkid;
      }

      public String getRealname() {
        return realname;
      }

      public void setRealname(String realname) {
        this.realname = realname;
      }

      public String getTargetids() {
        return targetids;
      }

      public void setTargetids(String targetids) {
        this.targetids = targetids;
      }

      public List<?> getInnerlist() {
        return innerlist;
      }

      public void setInnerlist(List<?> innerlist) {
        this.innerlist = innerlist;
      }

      public static class ContenttypeJsonBean implements Serializable{
        /**
         * type : image
         * contentImgId : 390b5fa8-50d0-46f2-afc6-0c478da35ea2
         */

        private String type;
        private String contentImgId;

        public String getType() {
          return type;
        }

        public void setType(String type) {
          this.type = type;
        }

        public String getContentImgId() {
          return contentImgId;
        }

        public void setContentImgId(String contentImgId) {
          this.contentImgId = contentImgId;
        }
      }
    }

    public static class JoinersBean implements Serializable{
      /**
       * UserId : 7fc8eb07-da6e-4912-b144-7a9247020f5a
       * EnCode : null
       * Account : null
       * Password : null
       * Secretkey : null
       * RealName : 王子文
       * NickName : null
       * HeadIcon : null
       * QuickQuery : null
       * SimpleSpelling : null
       * Gender : null
       * Birthday : null
       * Mobile :
       * Telephone : null
       * Email :
       * OICQ : null
       * WeChat : null
       * MSN : null
       * ManagerId : null
       * Manager : null
       * OrganizeId : null
       * DepartmentId : null
       * RoleId : null
       * DutyId : null
       * DutyName : null
       * PostId : null
       * PostName : null
       * WorkGroupId : null
       * SecurityLevel : null
       * UserOnLine : null
       * OpenId : null
       * Question : null
       * AnswerQuestion : null
       * CheckOnLine : null
       * AllowStartTime : null
       * AllowEndTime : null
       * LockStartDate : null
       * LockEndDate : null
       * FirstVisit : null
       * PreviousVisit : null
       * LastVisit : null
       * LogOnCount : null
       * SortCode : null
       * DeleteMark : null
       * EnabledMark : null
       * Description : null
       * CreateDate : null
       * CreateUserId : null
       * CreateUserName : null
       * ModifyDate : null
       * ModifyUserId : null
       * ModifyUserName : null
       * UserType : null
       * DataFrom : null
       */

      private String Id;
      private String ol_guid;

      public String getOl_guid() {
        return ol_guid;
      }

      public void setOl_guid(String ol_guid) {
        this.ol_guid = ol_guid;
      }

      private Object EnCode;
      private Object Account;
      private Object Password;
      private Object Secretkey;
      private String RealName;
      private Object NickName;
      private Object HeadIcon;
      private Object QuickQuery;
      private Object SimpleSpelling;
      private Object Gender;
      private Object Birthday;
      private String Mobile;
      private Object Telephone;
      private String Email;
      private Object OICQ;
      private Object WeChat;
      private Object MSN;
      private Object ManagerId;
      private Object Manager;
      private Object OrganizeId;
      private Object DepartmentId;
      private Object RoleId;
      private Object DutyId;
      private Object DutyName;
      private Object PostId;
      private Object PostName;
      private Object WorkGroupId;
      private Object SecurityLevel;
      private Object UserOnLine;
      private Object OpenId;
      private Object Question;
      private Object AnswerQuestion;
      private Object CheckOnLine;
      private Object AllowStartTime;
      private Object AllowEndTime;
      private Object LockStartDate;
      private Object LockEndDate;
      private Object FirstVisit;
      private Object PreviousVisit;
      private Object LastVisit;
      private Object LogOnCount;
      private Object SortCode;
      private Object DeleteMark;
      private Object EnabledMark;
      private Object Description;
      private Object CreateDate;
      private Object CreateUserId;
      private Object CreateUserName;
      private Object ModifyDate;
      private Object ModifyUserId;
      private Object ModifyUserName;
      private Object UserType;
      private Object DataFrom;

      public String getId() {
        return Id;
      }

      public void setId(String id) {
        Id = id;
      }

      public Object getEnCode() {
        return EnCode;
      }

      public void setEnCode(Object EnCode) {
        this.EnCode = EnCode;
      }

      public Object getAccount() {
        return Account;
      }

      public void setAccount(Object Account) {
        this.Account = Account;
      }

      public Object getPassword() {
        return Password;
      }

      public void setPassword(Object Password) {
        this.Password = Password;
      }

      public Object getSecretkey() {
        return Secretkey;
      }

      public void setSecretkey(Object Secretkey) {
        this.Secretkey = Secretkey;
      }

      public String getRealName() {
        return RealName;
      }

      public void setRealName(String RealName) {
        this.RealName = RealName;
      }

      public Object getNickName() {
        return NickName;
      }

      public void setNickName(Object NickName) {
        this.NickName = NickName;
      }

      public Object getHeadIcon() {
        return HeadIcon;
      }

      public void setHeadIcon(Object HeadIcon) {
        this.HeadIcon = HeadIcon;
      }

      public Object getQuickQuery() {
        return QuickQuery;
      }

      public void setQuickQuery(Object QuickQuery) {
        this.QuickQuery = QuickQuery;
      }

      public Object getSimpleSpelling() {
        return SimpleSpelling;
      }

      public void setSimpleSpelling(Object SimpleSpelling) {
        this.SimpleSpelling = SimpleSpelling;
      }

      public Object getGender() {
        return Gender;
      }

      public void setGender(Object Gender) {
        this.Gender = Gender;
      }

      public Object getBirthday() {
        return Birthday;
      }

      public void setBirthday(Object Birthday) {
        this.Birthday = Birthday;
      }

      public String getMobile() {
        return Mobile;
      }

      public void setMobile(String Mobile) {
        this.Mobile = Mobile;
      }

      public Object getTelephone() {
        return Telephone;
      }

      public void setTelephone(Object Telephone) {
        this.Telephone = Telephone;
      }

      public String getEmail() {
        return Email;
      }

      public void setEmail(String Email) {
        this.Email = Email;
      }

      public Object getOICQ() {
        return OICQ;
      }

      public void setOICQ(Object OICQ) {
        this.OICQ = OICQ;
      }

      public Object getWeChat() {
        return WeChat;
      }

      public void setWeChat(Object WeChat) {
        this.WeChat = WeChat;
      }

      public Object getMSN() {
        return MSN;
      }

      public void setMSN(Object MSN) {
        this.MSN = MSN;
      }

      public Object getManagerId() {
        return ManagerId;
      }

      public void setManagerId(Object ManagerId) {
        this.ManagerId = ManagerId;
      }

      public Object getManager() {
        return Manager;
      }

      public void setManager(Object Manager) {
        this.Manager = Manager;
      }

      public Object getOrganizeId() {
        return OrganizeId;
      }

      public void setOrganizeId(Object OrganizeId) {
        this.OrganizeId = OrganizeId;
      }

      public Object getDepartmentId() {
        return DepartmentId;
      }

      public void setDepartmentId(Object DepartmentId) {
        this.DepartmentId = DepartmentId;
      }

      public Object getRoleId() {
        return RoleId;
      }

      public void setRoleId(Object RoleId) {
        this.RoleId = RoleId;
      }

      public Object getDutyId() {
        return DutyId;
      }

      public void setDutyId(Object DutyId) {
        this.DutyId = DutyId;
      }

      public Object getDutyName() {
        return DutyName;
      }

      public void setDutyName(Object DutyName) {
        this.DutyName = DutyName;
      }

      public Object getPostId() {
        return PostId;
      }

      public void setPostId(Object PostId) {
        this.PostId = PostId;
      }

      public Object getPostName() {
        return PostName;
      }

      public void setPostName(Object PostName) {
        this.PostName = PostName;
      }

      public Object getWorkGroupId() {
        return WorkGroupId;
      }

      public void setWorkGroupId(Object WorkGroupId) {
        this.WorkGroupId = WorkGroupId;
      }

      public Object getSecurityLevel() {
        return SecurityLevel;
      }

      public void setSecurityLevel(Object SecurityLevel) {
        this.SecurityLevel = SecurityLevel;
      }

      public Object getUserOnLine() {
        return UserOnLine;
      }

      public void setUserOnLine(Object UserOnLine) {
        this.UserOnLine = UserOnLine;
      }

      public Object getOpenId() {
        return OpenId;
      }

      public void setOpenId(Object OpenId) {
        this.OpenId = OpenId;
      }

      public Object getQuestion() {
        return Question;
      }

      public void setQuestion(Object Question) {
        this.Question = Question;
      }

      public Object getAnswerQuestion() {
        return AnswerQuestion;
      }

      public void setAnswerQuestion(Object AnswerQuestion) {
        this.AnswerQuestion = AnswerQuestion;
      }

      public Object getCheckOnLine() {
        return CheckOnLine;
      }

      public void setCheckOnLine(Object CheckOnLine) {
        this.CheckOnLine = CheckOnLine;
      }

      public Object getAllowStartTime() {
        return AllowStartTime;
      }

      public void setAllowStartTime(Object AllowStartTime) {
        this.AllowStartTime = AllowStartTime;
      }

      public Object getAllowEndTime() {
        return AllowEndTime;
      }

      public void setAllowEndTime(Object AllowEndTime) {
        this.AllowEndTime = AllowEndTime;
      }

      public Object getLockStartDate() {
        return LockStartDate;
      }

      public void setLockStartDate(Object LockStartDate) {
        this.LockStartDate = LockStartDate;
      }

      public Object getLockEndDate() {
        return LockEndDate;
      }

      public void setLockEndDate(Object LockEndDate) {
        this.LockEndDate = LockEndDate;
      }

      public Object getFirstVisit() {
        return FirstVisit;
      }

      public void setFirstVisit(Object FirstVisit) {
        this.FirstVisit = FirstVisit;
      }

      public Object getPreviousVisit() {
        return PreviousVisit;
      }

      public void setPreviousVisit(Object PreviousVisit) {
        this.PreviousVisit = PreviousVisit;
      }

      public Object getLastVisit() {
        return LastVisit;
      }

      public void setLastVisit(Object LastVisit) {
        this.LastVisit = LastVisit;
      }

      public Object getLogOnCount() {
        return LogOnCount;
      }

      public void setLogOnCount(Object LogOnCount) {
        this.LogOnCount = LogOnCount;
      }

      public Object getSortCode() {
        return SortCode;
      }

      public void setSortCode(Object SortCode) {
        this.SortCode = SortCode;
      }

      public Object getDeleteMark() {
        return DeleteMark;
      }

      public void setDeleteMark(Object DeleteMark) {
        this.DeleteMark = DeleteMark;
      }

      public Object getEnabledMark() {
        return EnabledMark;
      }

      public void setEnabledMark(Object EnabledMark) {
        this.EnabledMark = EnabledMark;
      }

      public Object getDescription() {
        return Description;
      }

      public void setDescription(Object Description) {
        this.Description = Description;
      }

      public Object getCreateDate() {
        return CreateDate;
      }

      public void setCreateDate(Object CreateDate) {
        this.CreateDate = CreateDate;
      }

      public Object getCreateUserId() {
        return CreateUserId;
      }

      public void setCreateUserId(Object CreateUserId) {
        this.CreateUserId = CreateUserId;
      }

      public Object getCreateUserName() {
        return CreateUserName;
      }

      public void setCreateUserName(Object CreateUserName) {
        this.CreateUserName = CreateUserName;
      }

      public Object getModifyDate() {
        return ModifyDate;
      }

      public void setModifyDate(Object ModifyDate) {
        this.ModifyDate = ModifyDate;
      }

      public Object getModifyUserId() {
        return ModifyUserId;
      }

      public void setModifyUserId(Object ModifyUserId) {
        this.ModifyUserId = ModifyUserId;
      }

      public Object getModifyUserName() {
        return ModifyUserName;
      }

      public void setModifyUserName(Object ModifyUserName) {
        this.ModifyUserName = ModifyUserName;
      }

      public Object getUserType() {
        return UserType;
      }

      public void setUserType(Object UserType) {
        this.UserType = UserType;
      }

      public Object getDataFrom() {
        return DataFrom;
      }

      public void setDataFrom(Object DataFrom) {
        this.DataFrom = DataFrom;
      }
    }

    public static class TagListBean implements Serializable{
      /*"it_guid":"0b412982-8d9f-47ae-a72e-0f232503a469",
         "it_name":"12",
                "it_color":"rgba(86, 98, 112, 1)",
                "it_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6"
       */

      private String it_guid;
      private String it_name;
      private String it_color;
      private String it_organizeId;

      public String getIt_guid() {
        return it_guid;
      }

      public void setIt_guid(String it_guid) {
        this.it_guid = it_guid;
      }

      public String getIt_name() {
        return it_name;
      }

      public void setIt_name(String it_name) {
        this.it_name = it_name;
      }

      public String getIt_color() {
        return it_color;
      }

      public void setIt_color(String it_color) {
        this.it_color = it_color;
      }

      public String getIt_organizeId() {
        return it_organizeId;
      }

      public void setIt_organizeId(String it_organizeId) {
        this.it_organizeId = it_organizeId;
      }
    }

    public void setTagList(
        List<TagListBean> tagList) {
      this.tagList = tagList;
    }
  }
}
