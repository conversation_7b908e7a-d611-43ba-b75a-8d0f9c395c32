package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

public class ExitResultDto {

    @SerializedName("Ret")
    private Integer ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataBean data;

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        @SerializedName("ApisessionId")
        private String apisessionId;
        @SerializedName("ApisessionToken")
        private String apisessionToken;
        @SerializedName("ApisessionCurrenttime")
        private String apisessionCurrenttime;
        @SerializedName("ApisessionCompanyid")
        private String apisessionCompanyid;
        @SerializedName("ApisessionUserid")
        private String apisessionUserid;
        @SerializedName("IfSingleLogin")
        private String ifSingleLogin;
        @SerializedName("UserName")
        private String userName;
        @SerializedName("Account")
        private String account;
        @SerializedName("Email")
        private String email;

        public String getApisessionId() {
            return apisessionId;
        }

        public void setApisessionId(String apisessionId) {
            this.apisessionId = apisessionId;
        }

        public String getApisessionToken() {
            return apisessionToken;
        }

        public void setApisessionToken(String apisessionToken) {
            this.apisessionToken = apisessionToken;
        }

        public String getApisessionCurrenttime() {
            return apisessionCurrenttime;
        }

        public void setApisessionCurrenttime(String apisessionCurrenttime) {
            this.apisessionCurrenttime = apisessionCurrenttime;
        }

        public String getApisessionCompanyid() {
            return apisessionCompanyid;
        }

        public void setApisessionCompanyid(String apisessionCompanyid) {
            this.apisessionCompanyid = apisessionCompanyid;
        }

        public String getApisessionUserid() {
            return apisessionUserid;
        }

        public void setApisessionUserid(String apisessionUserid) {
            this.apisessionUserid = apisessionUserid;
        }

        public String getIfSingleLogin() {
            return ifSingleLogin;
        }

        public void setIfSingleLogin(String ifSingleLogin) {
            this.ifSingleLogin = ifSingleLogin;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }
    }
}
