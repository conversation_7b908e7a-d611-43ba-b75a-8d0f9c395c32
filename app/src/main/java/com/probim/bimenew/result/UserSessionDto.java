package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

public class UserSessionDto {

    @SerializedName("Ret")
    private Integer ret;
    @SerializedName("Msg")
    private String msg;
    @SerializedName("Data")
    private DataBean data;

    public Integer getRet() {
        return ret;
    }

    public void setRet(Integer ret) {
        this.ret = ret;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        @SerializedName("apisession_token")
        private String apisessionToken;
        @SerializedName("apisession_userid")
        private String apisessionUserid;

        public String getApisessionToken() {
            return apisessionToken;
        }

        public void setApisessionToken(String apisessionToken) {
            this.apisessionToken = apisessionToken;
        }

        public String getApisessionUserid() {
            return apisessionUserid;
        }

        public void setApisessionUserid(String apisessionUserid) {
            this.apisessionUserid = apisessionUserid;
        }
    }
}
