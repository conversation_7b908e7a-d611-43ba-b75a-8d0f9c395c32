package com.probim.bimenew.result;

import java.util.List;

/**
 * Description :
 * Author : <PERSON>
 * Email  : <EMAIL>
 * Date   : 2019/5/6/16:39.
 */

public class AuthorizeResult {

  /**
   * Ret : 1
   * Msg : OK
   * Data : [{"Bm_ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","Bmbs":[{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"9432dd05-0321-417d-b27a-86606650e252","Bmb_FullName":"可见","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"fffa5bfe-fb6e-48db-be2d-84d08104d80e","Bmb_FullName":"新增","bm_EnCode":"BIMModel","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"ba8c23bb-5da2-4fb1-a260-a207b1fd33d7","Bmb_FullName":"编辑","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"665a922f-6dbe-4222-ae63-1973fd31ba1c","Bmb_FullName":"删除","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"1f20b657-cbef-499d-981f-b19cb84be447","Bmb_FullName":"上传","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"20a72b6b-a31c-4c3c-b9cc-2e98cd2b62d8","Bmb_FullName":"合并","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"d45aaa48-e882-4d0e-b7fc-79ba3cdea2c5","Bmb_FullName":"设置默认视图","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"d76adba0-dc8c-4d35-8c0b-a8fb931c3e71","Bm_FullName":"GIS","Bmbs":[{"ModuleId":"d76adba0-dc8c-4d35-8c0b-a8fb931c3e71","Bm_FullName":"GIS","ModuleButtonId":"3cfa154e-86dc-4646-b311-20d6f07a5e37","Bmb_FullName":"可见","bm_EnCode":"GIS","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","Bmbs":[{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"c5682e05-bd14-4347-948b-c2ba9ada9f8b","Bmb_FullName":"可见","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"dc081752-8a32-4fd5-8a7e-463994d92b88","Bmb_FullName":"上传文件","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"8170264c-de71-435c-bb2f-029cdbe6a1d0","Bmb_FullName":"新建文件夹","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"7223737a-99d0-4f4a-8f48-8705258e7c45","Bmb_FullName":"删除","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"ebbe1265-1451-49cd-88ee-be7a9089f180","Bmb_FullName":"重命名","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"efeb0b9e-4a14-4860-a175-772636ce7556","Bmb_FullName":"下载","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"92013023-c963-42f4-b740-0acb1f3998ef","Bmb_FullName":"查看历史版本","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"62720308-5c3e-4c8f-abd4-0182dc804b37","Bm_FullName":"项目文档","ModuleButtonId":"12e02512-0b17-40cd-a188-31aa772959a7","Bmb_FullName":"新建关联","bm_EnCode":"Document","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"833b231a-eb35-4d47-b88b-e0fde7290128","Bm_FullName":"问题追踪","Bmbs":[{"ModuleId":"833b231a-eb35-4d47-b88b-e0fde7290128","Bm_FullName":"问题追踪","ModuleButtonId":"a2337587-d524-4f4f-8148-23c106b9aa39","Bmb_FullName":"可见","bm_EnCode":"IssueTracking","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"833b231a-eb35-4d47-b88b-e0fde7290128","Bm_FullName":"问题追踪","ModuleButtonId":"b34cac97-2c3d-498d-b1ba-714cdf29f420","Bmb_FullName":"新增问题","bm_EnCode":"IssueTracking","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"929e5231-ae2a-4543-a55c-c58046fefe4f","Bm_FullName":"我的任务","Bmbs":[{"ModuleId":"929e5231-ae2a-4543-a55c-c58046fefe4f","Bm_FullName":"我的任务","ModuleButtonId":"369b3857-a116-46f6-975b-ecb6c254c757","Bmb_FullName":"可见","bm_EnCode":"MyMissions","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","Bmbs":[{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"e0d1559f-0773-444e-9a1a-1c21312323c4","Bmb_FullName":"刷新","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"18a179d0-99f2-4ce2-b96a-553ab2d59218","Bmb_FullName":"详细","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"58533757-cf50-4890-94b1-557aa374f575","Bmb_FullName":"数据可见","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"9806ecc1-aae3-422b-9919-aa3a0f7c430c","Bmb_FullName":"可见","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"3474c0c8-ffe1-46d8-9d26-aa5e5beb626d","Bmb_FullName":"导出","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"e6110f46-669f-4cc4-87c3-9b620bf34d8f","Bmb_FullName":"聚焦","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"3b4f144f-e9c4-4287-8e20-d34fd9d03720","Bmb_FullName":"添加标签","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"059fd175-7d91-4b97-9a89-d0446f294be2","Bm_FullName":"现场数据","ModuleButtonId":"81163676-95db-45d8-bf71-68c8a79748ed","Bmb_FullName":"删除标签","bm_EnCode":"QualityManage","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"},{"Bm_ModuleId":"5569d697-a4ec-4f68-a22c-cfd66d16d0e8","Bm_FullName":"项目流程","Bmbs":[{"ModuleId":"5569d697-a4ec-4f68-a22c-cfd66d16d0e8","Bm_FullName":"项目流程","ModuleButtonId":"b7af46e3-4086-4613-87da-9fcfe62cb492","Bmb_FullName":"可见","bm_EnCode":"XMLC","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}],"checkstate":"1"}]
   */

  private int Ret;
  private String Msg;
  private List<DataBean> Data;

  public int getRet() {
    return Ret;
  }

  public void setRet(int Ret) {
    this.Ret = Ret;
  }

  public String getMsg() {
    return Msg;
  }

  public void setMsg(String Msg) {
    this.Msg = Msg;
  }

  public List<DataBean> getData() {
    return Data;
  }

  public void setData(List<DataBean> Data) {
    this.Data = Data;
  }

  public static class DataBean {
    /**
     * Bm_ModuleId : f4769b9c-3d07-4c26-b05c-648deffd312b
     * Bm_FullName : 模型列表
     * Bmbs : [{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"9432dd05-0321-417d-b27a-86606650e252","Bmb_FullName":"可见","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"fffa5bfe-fb6e-48db-be2d-84d08104d80e","Bmb_FullName":"新增","bm_EnCode":"BIMModel","Roles":[{"RoleId":"7d240224-cd1b-47b4-9866-d468332c9fb0","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"项目管理员","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null},{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"ba8c23bb-5da2-4fb1-a260-a207b1fd33d7","Bmb_FullName":"编辑","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"665a922f-6dbe-4222-ae63-1973fd31ba1c","Bmb_FullName":"删除","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"1f20b657-cbef-499d-981f-b19cb84be447","Bmb_FullName":"上传","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"20a72b6b-a31c-4c3c-b9cc-2e98cd2b62d8","Bmb_FullName":"合并","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"},{"ModuleId":"f4769b9c-3d07-4c26-b05c-648deffd312b","Bm_FullName":"模型列表","ModuleButtonId":"d45aaa48-e882-4d0e-b7fc-79ba3cdea2c5","Bmb_FullName":"设置默认视图","bm_EnCode":"BIMModel","Roles":[{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}],"checkstate":"1"}]
     * checkstate : 1
     */

    private String Bm_ModuleId;
    private String Bm_FullName;
    private int checkstate;
    private List<BmbsBean> Bmbs;

    public String getBm_EnCode() {
      return Bm_EnCode;
    }

    public void setBm_EnCode(String bm_EnCode) {
      Bm_EnCode = bm_EnCode;
    }

    private String Bm_EnCode;


    public String getBm_ModuleId() {
      return Bm_ModuleId;
    }

    public void setBm_ModuleId(String Bm_ModuleId) {
      this.Bm_ModuleId = Bm_ModuleId;
    }

    public String getBm_FullName() {
      return Bm_FullName;
    }

    public void setBm_FullName(String Bm_FullName) {
      this.Bm_FullName = Bm_FullName;
    }

    public int getCheckstate() {
      return checkstate;
    }

    public void setCheckstate(int checkstate) {
      this.checkstate = checkstate;
    }

    public List<BmbsBean> getBmbs() {
      return Bmbs;
    }

    public void setBmbs(List<BmbsBean> Bmbs) {
      this.Bmbs = Bmbs;
    }

    public static class BmbsBean {
      /**
       * ModuleId : f4769b9c-3d07-4c26-b05c-648deffd312b
       * Bm_FullName : 模型列表
       * ModuleButtonId : 9432dd05-0321-417d-b27a-86606650e252
       * Bmb_FullName : 可见
       * bm_EnCode : BIMModel
       * Roles : [{"RoleId":"ac6ba88b-1598-4d52-a1e5-933a72cf02e7","OrganizeId":null,"Category":null,"EnCode":null,"FullName":"Public","IsPublic":null,"OverdueTime":null,"SortCode":null,"DeleteMark":null,"EnabledMark":null,"Description":null,"CreateDate":null,"CreateUserId":null,"CreateUserName":null,"ModifyDate":null,"ModifyUserId":null,"ModifyUserName":null,"IsProjectManager":null,"TemplateId":null}]
       * checkstate : 1
       */

      private String ModuleId;
      private String Bm_FullName;
      private String ModuleButtonId;
      private String Bmb_FullName;
      private String bm_EnCode;
      private int checkstate;
      private String Bmb_EnCode;

      public String getBmb_EnCode() {
        return Bmb_EnCode;
      }

      public void setBmb_EnCode(String bmb_EnCode) {
        Bmb_EnCode = bmb_EnCode;
      }

      private List<RolesBean> Roles;

      public String getModuleId() {
        return ModuleId;
      }

      public void setModuleId(String ModuleId) {
        this.ModuleId = ModuleId;
      }

      public String getBm_FullName() {
        return Bm_FullName;
      }

      public void setBm_FullName(String Bm_FullName) {
        this.Bm_FullName = Bm_FullName;
      }

      public String getModuleButtonId() {
        return ModuleButtonId;
      }

      public void setModuleButtonId(String ModuleButtonId) {
        this.ModuleButtonId = ModuleButtonId;
      }

      public String getBmb_FullName() {
        return Bmb_FullName;
      }

      public void setBmb_FullName(String Bmb_FullName) {
        this.Bmb_FullName = Bmb_FullName;
      }

      public String getBm_EnCode() {
        return bm_EnCode;
      }

      public void setBm_EnCode(String bm_EnCode) {
        this.bm_EnCode = bm_EnCode;
      }

      public int getCheckstate() {
        return checkstate;
      }

      public void setCheckstate(int checkstate) {
        this.checkstate = checkstate;
      }

      public List<RolesBean> getRoles() {
        return Roles;
      }

      public void setRoles(List<RolesBean> Roles) {
        this.Roles = Roles;
      }

      public static class RolesBean {
        /**
         * RoleId : ac6ba88b-1598-4d52-a1e5-933a72cf02e7
         * OrganizeId : null
         * Category : null
         * EnCode : null
         * FullName : Public
         * IsPublic : null
         * OverdueTime : null
         * SortCode : null
         * DeleteMark : null
         * EnabledMark : null
         * Description : null
         * CreateDate : null
         * CreateUserId : null
         * CreateUserName : null
         * ModifyDate : null
         * ModifyUserId : null
         * ModifyUserName : null
         * IsProjectManager : null
         * TemplateId : null
         */

        private String RoleId;
        private Object OrganizeId;
        private Object Category;
        private Object EnCode;
        private String FullName;
        private Object IsPublic;
        private Object OverdueTime;
        private Object SortCode;
        private Object DeleteMark;
        private Object EnabledMark;
        private Object Description;
        private Object CreateDate;
        private Object CreateUserId;
        private Object CreateUserName;
        private Object ModifyDate;
        private Object ModifyUserId;
        private Object ModifyUserName;
        private Object IsProjectManager;
        private Object TemplateId;

        public String getRoleId() {
          return RoleId;
        }

        public void setRoleId(String RoleId) {
          this.RoleId = RoleId;
        }

        public Object getOrganizeId() {
          return OrganizeId;
        }

        public void setOrganizeId(Object OrganizeId) {
          this.OrganizeId = OrganizeId;
        }

        public Object getCategory() {
          return Category;
        }

        public void setCategory(Object Category) {
          this.Category = Category;
        }

        public Object getEnCode() {
          return EnCode;
        }

        public void setEnCode(Object EnCode) {
          this.EnCode = EnCode;
        }

        public String getFullName() {
          return FullName;
        }

        public void setFullName(String FullName) {
          this.FullName = FullName;
        }

        public Object getIsPublic() {
          return IsPublic;
        }

        public void setIsPublic(Object IsPublic) {
          this.IsPublic = IsPublic;
        }

        public Object getOverdueTime() {
          return OverdueTime;
        }

        public void setOverdueTime(Object OverdueTime) {
          this.OverdueTime = OverdueTime;
        }

        public Object getSortCode() {
          return SortCode;
        }

        public void setSortCode(Object SortCode) {
          this.SortCode = SortCode;
        }

        public Object getDeleteMark() {
          return DeleteMark;
        }

        public void setDeleteMark(Object DeleteMark) {
          this.DeleteMark = DeleteMark;
        }

        public Object getEnabledMark() {
          return EnabledMark;
        }

        public void setEnabledMark(Object EnabledMark) {
          this.EnabledMark = EnabledMark;
        }

        public Object getDescription() {
          return Description;
        }

        public void setDescription(Object Description) {
          this.Description = Description;
        }

        public Object getCreateDate() {
          return CreateDate;
        }

        public void setCreateDate(Object CreateDate) {
          this.CreateDate = CreateDate;
        }

        public Object getCreateUserId() {
          return CreateUserId;
        }

        public void setCreateUserId(Object CreateUserId) {
          this.CreateUserId = CreateUserId;
        }

        public Object getCreateUserName() {
          return CreateUserName;
        }

        public void setCreateUserName(Object CreateUserName) {
          this.CreateUserName = CreateUserName;
        }

        public Object getModifyDate() {
          return ModifyDate;
        }

        public void setModifyDate(Object ModifyDate) {
          this.ModifyDate = ModifyDate;
        }

        public Object getModifyUserId() {
          return ModifyUserId;
        }

        public void setModifyUserId(Object ModifyUserId) {
          this.ModifyUserId = ModifyUserId;
        }

        public Object getModifyUserName() {
          return ModifyUserName;
        }

        public void setModifyUserName(Object ModifyUserName) {
          this.ModifyUserName = ModifyUserName;
        }

        public Object getIsProjectManager() {
          return IsProjectManager;
        }

        public void setIsProjectManager(Object IsProjectManager) {
          this.IsProjectManager = IsProjectManager;
        }

        public Object getTemplateId() {
          return TemplateId;
        }

        public void setTemplateId(Object TemplateId) {
          this.TemplateId = TemplateId;
        }
      }
    }
  }
}
