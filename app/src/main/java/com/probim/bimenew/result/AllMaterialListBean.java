package com.probim.bimenew.result;

import java.io.Serializable;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2020/10/27/16:34
 */
public class AllMaterialListBean implements Serializable {

    private static final long serialVersionUID = 6643141852896191780L;


    /**
     * Ret : 1
     * Msg : OK
     * Data : {"list":[{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"d73152fd-eb59-4fe5-8927-7f330a1205ca","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"1","bmc_code":"011","bmc_currentNumber":11,"bmc_createTime":"2020-09-08T15:50:57","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"328eb4cf-4407-4c78-8187-523191d560b2","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"121","bmc_code":"012","bmc_currentNumber":12,"bmc_createTime":"2020-09-08T15:51:11","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"410eee11-5c11-4a94-afd1-f898904f8f41","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"3","bmc_code":"003","bmc_currentNumber":3,"bmc_createTime":"2020-07-13T17:43:03","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"53c9f5fc-4c87-4e3d-85ad-4873954af34c","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"411","bmc_code":"004","bmc_currentNumber":4,"bmc_createTime":"2020-07-13T17:43:15","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"cebe0c2f-85a5-43ac-a129-b6a5567147b1","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"66","bmc_code":"005","bmc_currentNumber":5,"bmc_createTime":"2020-07-14T11:04:48","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":2,"ChildrenItemCount":0,"bmc_guid":"f110341d-6549-4986-9c43-41057f5a4203","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"7","bmc_code":"006","bmc_currentNumber":6,"bmc_createTime":"2020-07-14T11:04:59","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":4,"ChildrenItemCount":0,"bmc_guid":"546f21f4-8dc3-4d52-80af-8a3b1fbb0762","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"分类2","bmc_code":"002","bmc_currentNumber":2,"bmc_createTime":"2020-07-13T17:43:00","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"bce0fe17-421a-4156-9ca5-3c346d0225ed","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"普通墙","bmc_code":"009","bmc_currentNumber":9,"bmc_createTime":"2020-07-15T16:09:04","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"8fb79487-66ee-4e6a-93bb-4e50bf8b7384","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"普通墙2","bmc_code":"010","bmc_currentNumber":10,"bmc_createTime":"2020-09-01T11:16:15","bmc_extjson":null,"bmc_elementids":null}],"totalcount":0}
     */

    private int Ret;
    private String Msg;
    private DataBean Data;

    public int getRet() {
        return Ret;
    }

    public void setRet(int Ret) {
        this.Ret = Ret;
    }

    public String getMsg() {
        return Msg;
    }

    public void setMsg(String Msg) {
        this.Msg = Msg;
    }

    public DataBean getData() {
        return Data;
    }

    public void setData(DataBean Data) {
        this.Data = Data;
    }

    public static class DataBean implements Serializable {
        /**
         * list : [{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"d73152fd-eb59-4fe5-8927-7f330a1205ca","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"1","bmc_code":"011","bmc_currentNumber":11,"bmc_createTime":"2020-09-08T15:50:57","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"328eb4cf-4407-4c78-8187-523191d560b2","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"121","bmc_code":"012","bmc_currentNumber":12,"bmc_createTime":"2020-09-08T15:51:11","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"410eee11-5c11-4a94-afd1-f898904f8f41","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"3","bmc_code":"003","bmc_currentNumber":3,"bmc_createTime":"2020-07-13T17:43:03","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":1,"ChildrenItemCount":0,"bmc_guid":"53c9f5fc-4c87-4e3d-85ad-4873954af34c","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"411","bmc_code":"004","bmc_currentNumber":4,"bmc_createTime":"2020-07-13T17:43:15","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"cebe0c2f-85a5-43ac-a129-b6a5567147b1","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"66","bmc_code":"005","bmc_currentNumber":5,"bmc_createTime":"2020-07-14T11:04:48","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":2,"ChildrenItemCount":0,"bmc_guid":"f110341d-6549-4986-9c43-41057f5a4203","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"7","bmc_code":"006","bmc_currentNumber":6,"bmc_createTime":"2020-07-14T11:04:59","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":4,"ChildrenItemCount":0,"bmc_guid":"546f21f4-8dc3-4d52-80af-8a3b1fbb0762","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"分类2","bmc_code":"002","bmc_currentNumber":2,"bmc_createTime":"2020-07-13T17:43:00","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"bce0fe17-421a-4156-9ca5-3c346d0225ed","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"普通墙","bmc_code":"009","bmc_currentNumber":9,"bmc_createTime":"2020-07-15T16:09:04","bmc_extjson":null,"bmc_elementids":null},{"DirectChildrenCount":0,"ChildrenItemCount":0,"bmc_guid":"8fb79487-66ee-4e6a-93bb-4e50bf8b7384","bmc_organizeId":"48617e7b-07f2-4748-9199-238af8f2bfc6","bmc_name":"普通墙2","bmc_code":"010","bmc_currentNumber":10,"bmc_createTime":"2020-09-01T11:16:15","bmc_extjson":null,"bmc_elementids":null}]
         * totalcount : 0
         */

        private int totalcount;
        private List<ListBean> list;

        public int getTotalcount() {
            return totalcount;
        }

        public void setTotalcount(int totalcount) {
            this.totalcount = totalcount;
        }

        public List<ListBean> getList() {
            return list;
        }

        public void setList(List<ListBean> list) {
            this.list = list;
        }

        public static class ListBean {
            /**
             * DirectChildrenCount : 0
             * ChildrenItemCount : 0
             * bmc_guid : d73152fd-eb59-4fe5-8927-7f330a1205ca
             * bmc_organizeId : 48617e7b-07f2-4748-9199-238af8f2bfc6
             * bmc_name : 1
             * bmc_code : 011
             * bmc_currentNumber : 11
             * bmc_createTime : 2020-09-08T15:50:57
             * bmc_extjson : null
             * bmc_elementids : null
             */

            private int DirectChildrenCount;
            private int ChildrenItemCount;
            private String bmc_guid;
            private String bmc_organizeId;
            private String bmc_name;
            private String bmc_code;
            private int bmc_currentNumber;
            private String bmc_createTime;
            private Object bmc_extjson;
            private Object bmc_elementids;
            private boolean isSelected;

            public boolean isSelected() {
                return isSelected;
            }

            public void setSelected(boolean selected) {
                isSelected = selected;
            }

            public int getDirectChildrenCount() {
                return DirectChildrenCount;
            }

            public void setDirectChildrenCount(int DirectChildrenCount) {
                this.DirectChildrenCount = DirectChildrenCount;
            }

            public int getChildrenItemCount() {
                return ChildrenItemCount;
            }

            public void setChildrenItemCount(int ChildrenItemCount) {
                this.ChildrenItemCount = ChildrenItemCount;
            }

            public String getBmc_guid() {
                return bmc_guid;
            }

            public void setBmc_guid(String bmc_guid) {
                this.bmc_guid = bmc_guid;
            }

            public String getBmc_organizeId() {
                return bmc_organizeId;
            }

            public void setBmc_organizeId(String bmc_organizeId) {
                this.bmc_organizeId = bmc_organizeId;
            }

            public String getBmc_name() {
                return bmc_name;
            }

            public void setBmc_name(String bmc_name) {
                this.bmc_name = bmc_name;
            }

            public String getBmc_code() {
                return bmc_code;
            }

            public void setBmc_code(String bmc_code) {
                this.bmc_code = bmc_code;
            }

            public int getBmc_currentNumber() {
                return bmc_currentNumber;
            }

            public void setBmc_currentNumber(int bmc_currentNumber) {
                this.bmc_currentNumber = bmc_currentNumber;
            }

            public String getBmc_createTime() {
                return bmc_createTime;
            }

            public void setBmc_createTime(String bmc_createTime) {
                this.bmc_createTime = bmc_createTime;
            }

            public Object getBmc_extjson() {
                return bmc_extjson;
            }

            public void setBmc_extjson(Object bmc_extjson) {
                this.bmc_extjson = bmc_extjson;
            }

            public Object getBmc_elementids() {
                return bmc_elementids;
            }

            public void setBmc_elementids(Object bmc_elementids) {
                this.bmc_elementids = bmc_elementids;
            }
        }
    }
}
