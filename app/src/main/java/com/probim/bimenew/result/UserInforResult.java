package com.probim.bimenew.result;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/5/23/16:29.
 */

public class UserInforResult {


  @SerializedName("Ret")
  private Integer ret;
  @SerializedName("Msg")
  private String msg;
  @SerializedName("Data")
  private DataBean data;

  public Integer getRet() {
    return ret;
  }

  public void setRet(Integer ret) {
    this.ret = ret;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public DataBean getData() {
    return data;
  }

  public void setData(DataBean data) {
    this.data = data;
  }

  public static class DataBean {
    @SerializedName("UserId")
    private String userId;
    @SerializedName("Account")
    private String account;
    @SerializedName("RealName")
    private String realName;
    @SerializedName("Mobile")
    private String mobile;
    @SerializedName("Email")
    private String email;
    @SerializedName("OrganizeId")
    private String organizeId;
    @SerializedName("DeleteMark")
    private Integer deleteMark;
    @SerializedName("EnabledMark")
    private Integer enabledMark;
    @SerializedName("CreateDate")
    private String createDate;
    @SerializedName("UserProjectInfos")
    private List<UserProjectInfosBean> userProjectInfos;

    public String getUserId() {
      return userId;
    }

    public void setUserId(String userId) {
      this.userId = userId;
    }

    public String getAccount() {
      return account;
    }

    public void setAccount(String account) {
      this.account = account;
    }

    public String getRealName() {
      return realName;
    }

    public void setRealName(String realName) {
      this.realName = realName;
    }

    public String getMobile() {
      return mobile;
    }

    public void setMobile(String mobile) {
      this.mobile = mobile;
    }

    public String getEmail() {
      return email;
    }

    public void setEmail(String email) {
      this.email = email;
    }

    public String getOrganizeId() {
      return organizeId;
    }

    public void setOrganizeId(String organizeId) {
      this.organizeId = organizeId;
    }

    public Integer getDeleteMark() {
      return deleteMark;
    }

    public void setDeleteMark(Integer deleteMark) {
      this.deleteMark = deleteMark;
    }

    public Integer getEnabledMark() {
      return enabledMark;
    }

    public void setEnabledMark(Integer enabledMark) {
      this.enabledMark = enabledMark;
    }

    public String getCreateDate() {
      return createDate;
    }

    public void setCreateDate(String createDate) {
      this.createDate = createDate;
    }

    public List<UserProjectInfosBean> getUserProjectInfos() {
      return userProjectInfos;
    }

    public void setUserProjectInfos(List<UserProjectInfosBean> userProjectInfos) {
      this.userProjectInfos = userProjectInfos;
    }

    public static class UserProjectInfosBean {
      @SerializedName("OrganizeId")
      private String organizeId;
      @SerializedName("OrganizeName")
      private String organizeName;
      @SerializedName("ProjectId")
      private String projectId;
      @SerializedName("ProjectName")
      private String projectName;
      @SerializedName("OrganizeEnableMark")
      private Integer organizeEnableMark;
      @SerializedName("ProjectEnableMark")
      private Integer projectEnableMark;

      public String getOrganizeId() {
        return organizeId;
      }

      public void setOrganizeId(String organizeId) {
        this.organizeId = organizeId;
      }

      public String getOrganizeName() {
        return organizeName;
      }

      public void setOrganizeName(String organizeName) {
        this.organizeName = organizeName;
      }

      public String getProjectId() {
        return projectId;
      }

      public void setProjectId(String projectId) {
        this.projectId = projectId;
      }

      public String getProjectName() {
        return projectName;
      }

      public void setProjectName(String projectName) {
        this.projectName = projectName;
      }

      public Integer getOrganizeEnableMark() {
        return organizeEnableMark;
      }

      public void setOrganizeEnableMark(Integer organizeEnableMark) {
        this.organizeEnableMark = organizeEnableMark;
      }

      public Integer getProjectEnableMark() {
        return projectEnableMark;
      }

      public void setProjectEnableMark(Integer projectEnableMark) {
        this.projectEnableMark = projectEnableMark;
      }
    }
  }
}
