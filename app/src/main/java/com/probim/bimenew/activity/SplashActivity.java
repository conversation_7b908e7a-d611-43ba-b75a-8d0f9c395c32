package com.probim.bimenew.activity;

import android.Manifest.permission;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
import android.view.View.OnClickListener;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.LoginController;
import com.probim.bimenew.dto.LoginDto;
import com.probim.bimenew.interfaces.PermissionListener;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.LoginResult;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.PwdUtlis;
import com.probim.bimenew.utils.SetAliasUtils;
import com.probim.bimenew.utils.Y5SignUtil;
import com.probim.bimenew.utils.view.AlertDialogUtil;
import java.util.List;
import pub.devrel.easypermissions.EasyPermissions;

/**
 * Description :启动页
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/12/13:17.
 */
public class SplashActivity extends BaseActivity implements EasyPermissions.PermissionCallbacks {

  private String[] perms = new String[] {
      permission.CAMERA, permission.WRITE_EXTERNAL_STORAGE,
      permission.READ_EXTERNAL_STORAGE,
  };

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    View decorView = getWindow().getDecorView();
    // Hide both the navigation bar and the status bar.
    // SYSTEM_UI_FLAG_FULLSCREEN is only available on Android 4.1 and higher, but as
    // a general rule, you should design your app to hide the status bar whenever you
    // hide the navigation bar.
    int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN;
    decorView.setSystemUiVisibility(uiOptions);
   /* requestWindowFeature(Window.FEATURE_NO_TITLE);
    getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
        WindowManager.LayoutParams.FLAG_FULLSCREEN);
*/
    //setContentView(R.layout.activity_splash);
    //        CheckPermission();
//    getPermission();
    toNext();
  }

  @Override
  protected void loadData() {
  }


  @Override
  protected void initView() {

  }

  @Override
  protected void initData() {

  }

  @Override
  protected void initRecycleview() {

  }

  @Override
  protected void initRefresh() {

  }

  /**
   * 进入主界面方法
   */
  private void toNext() {

        if (Hawk.contains(CustomParam.IsLogin)) {

          if (Hawk.get(CustomParam.IsLogin)) {
            String account = Hawk.get(CustomParam.LOGINACCOUNT);
            String passWord = Hawk.get(CustomParam.LOGINPWD);
            login(account,PwdUtlis.decodeString(passWord));
            Logger.t("登录").e(account+passWord);
          /*  SetAliasUtils.setAlias(Hawk.get(CustomParam.Alpha), SplashActivity.this);
            Logger.t("Alpha").e(Hawk.get(CustomParam.Alpha));
            startActivity(MainProjectActivity.class);
            finish();*/
          } else {

            startActivity(LoginActivity.class);
            finish();
          }
        } else {
         /* Hawk.put(CustomParam.Web_URL, "https://www.probim.cn:8076");
          Hawk.put(CustomParam.Bim_URL, "https://bimcomposer.probim.cn");
          Hawk.put(CustomParam.Base_URL, "https://api.probim.cn");*/
//          Hawk.put(CustomParam.Web_URL, "https://web-dcpmp.bnah.com.cn");
//          Hawk.put(CustomParam.Bim_URL, "https://web-model-dcpmp.bnah.com.cn");
//          Hawk.put(CustomParam.Base_URL, "https://bnah-web-api.biaddti.com");
          Hawk.put(CustomParam.CompanyCode, "300036");
          Hawk.put(CustomParam.CompanyIP, ApiConstant.GET_COMPANY_IP_NEW);
          Hawk.put(CustomParam.CompanyStr, "app公有云");
          startActivity(LoginActivity.class);
          finish();
        }
  }

  @Override
  protected void onResume() {
    super.onResume();
  }

  private void getPermission() {

    if (EasyPermissions.hasPermissions(this, perms)) {

      toNext();
    } else {

      EasyPermissions.requestPermissions(this, "权限申请",
          0, perms);
    }
  }

  @Override
  public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
      @NonNull int[] grantResults) {

    super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this);
  }

  //下面两个方法是实现EasyPermissions的EasyPermissions.PermissionCallbacks接口
  //分别返回授权成功和失败的权限
  @Override
  public void onPermissionsGranted(int requestCode, List<String> perms) {
    toNext();
  }

  @Override
  public void onPermissionsDenied(int requestCode, List<String> perms) {

  }
  /**
   * 登陆2
   */
  private void login(String phone, String pwd) {

    LoginController controller = new LoginController();
    LoginDto loginDto = new LoginDto();
    loginDto.setUserName(phone);
    loginDto.setPassword(Y5SignUtil.AESPKS7Encrypt(pwd + ApiConstant.DES_KEY));
    controller.Login2(JsonHelper.toJson(loginDto), new CallBack<LoginResult>() {
      @Override
      public void onSuccess(LoginResult loginResult) {

        if (ApiConstant.CODE_TYPE_SUCCESS == loginResult.getRet()) {
          //Hawk.put(CustomParam.ACCOUNT, loginModel.getResultData().getAccount());
          Hawk.put(CustomParam.IsLogin, true);
          Hawk.put(CustomParam.Token, loginResult.getData().getToken());
          Hawk.put(CustomParam.Alpha, loginResult.getData().getAlpha().replace("+", "-"));
          Hawk.put(CustomParam.LOGINACCOUNT,phone);
          Hawk.put(CustomParam.LOGINPWD, PwdUtlis.encryptionString(pwd));
          SetAliasUtils.setAlias(loginResult.getData().getAlpha(), SplashActivity.this);
          Intent intent = new Intent(SplashActivity.this,MainActivity.class);
          intent.putExtra("Token",loginResult.getData().getToken());
          startActivity(intent);
          finish();
        } else {
          startActivity(LoginActivity.class);
          finish();
        }

      }

      @Override
      public void onFail(String erroMsg) {
        showMsg(erroMsg);
        mLoading.dismiss();
      }
    });
  }


}
