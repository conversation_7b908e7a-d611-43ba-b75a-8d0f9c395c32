package com.probim.bimenew.activity;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.check.TranslucentUtils;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.LoginController;
import com.probim.bimenew.controller.UpdateController;
import com.probim.bimenew.dto.ExitDto;
import com.probim.bimenew.model.ProblemStatusModel;
import com.probim.bimenew.model.UpdateDto;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.ExitResultDto;
import com.probim.bimenew.update.AutoUpdater;
import com.probim.bimenew.utils.FileUtils;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.SetAliasUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class SettingActivity extends BaseActivity implements View.OnClickListener {

    private TextView tvCacheSize;
    private ImageView ivUpdate;
    private String appUrl;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting);
        TranslucentUtils.setTRANSLUCENT(this);
        initView();
    }

    @Override
    protected void onResume() {
        super.onResume();
        getAppVersions();

    }
    private void getAppVersions(){
        UpdateController controller = new UpdateController();
        HashMap<String,String> params = new HashMap<>();
        params.put("Token",Hawk.get(CustomParam.Token));
        params.put("PageNum","1");
        params.put("PageSize","999");
        controller.getAppVersions(params, new CallBack<UpdateDto>() {


            @Override
            public void onSuccess(UpdateDto updateDto) {
                if (updateDto.getRet() == 1){
                    if (!updateDto.getData().getData().isEmpty()){
                        String appCode = updateDto.getData().getData().get(0).getAppCode();
                        int code;
                        if (appCode.contains(".")){
                            code = Integer.parseInt(appCode.replace(".",""));
                        }else {
                            code = Integer.parseInt(appCode);
                        }
                        if (code > getAppVersionCode()){

                            ivUpdate.setVisibility(View.VISIBLE);
                            appUrl = Hawk.get(CustomParam.Base_URL) + ApiConstant.App_Download + "?Token=" + Hawk.get(CustomParam.Token) + "&AppType=Android";
                        }
                    }
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        ivUpdate = findView(R.id.iv_update);
        RelativeLayout rlUpdate = findView(R.id.rl_update, this);
        TextView tvUserName = findView(R.id.tv_name);
        tvCacheSize = findView(R.id.tv_cache_size);
        tvCacheSize.setText(FileUtils.getFormatSize(FileUtils.getFolderSize(new File(Environment.getExternalStorageDirectory() + "/BIMe"))));
        tvUserName.setText(Hawk.get(CustomParam.RealName));
        TextView tvCompantName = findView(R.id.tv_company_name);
        tvCompantName.setText(Hawk.get(CustomParam.CompanyStr));
        Button button = findView(R.id.btn_exit, this);
        RelativeLayout rlModifyPassWord = findView(R.id.rl_setting_password, this);
        TextView tvPhone = findView(R.id.tv_phone);
        tvPhone.setText(Hawk.get(CustomParam.PHONE));
        TextView tvEmail = findView(R.id.tv_email);
        tvEmail.setText(Hawk.get(CustomParam.EMAIL));
        LinearLayout linBack = findViewById(R.id.lin_back, this);
        TextView tvTittle = findView(R.id.tv_black_title);
        tvTittle.setText("个人设置");
        RelativeLayout rlDeleteCache = findView(R.id.rl_setting_delete_cache, this);
        TextView tvAppVersion = findView(R.id.tv_app_version);
        tvAppVersion.setText(getAppVersionName() + ".2024.0814");


    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.btn_exit:
                exit();
                break;

            case R.id.rl_setting_password:

                startActivity(ModifyPasswordActivity.class);

                break;

            case R.id.lin_about:
                finish();
                break;
            case R.id.rl_setting_delete_cache:
                FileUtils.deleteDir(new File(Environment.getExternalStorageDirectory() + "/BIMe"));
                tvCacheSize.setText(FileUtils.getFormatSize(FileUtils.getFolderSize(new File(Environment.getExternalStorageDirectory() + "/BIMe"))));
                break;
            case R.id.lin_back:
                finish();
                break;
            case R.id.rl_update:
                if (ivUpdate.getVisibility() == View.VISIBLE) {
                    update();
                }
                break;
            default:
                break;

        }
    }

    private void exit() {
        LoginController loginController = new LoginController();
        ExitDto dto = new ExitDto(Hawk.get(CustomParam.Token));
        loginController.exit(JsonHelper.toJson(dto), new CallBack<ExitResultDto>() {

            @Override
            public void onSuccess(ExitResultDto exitResultDto) {
                if (ApiConstant.CODE_TYPE_SUCCESS == exitResultDto.getRet()) {
                    showMsg("退出登录成功");
                    SetAliasUtils.deleteAlias(SettingActivity.this);
                    Hawk.delete(CustomParam.UserId);
                    Hawk.delete(CustomParam.RealName);
                    Hawk.put(CustomParam.IsLogin, false);
                    Hawk.delete(CustomParam.Alpha);
                    Hawk.put(CustomParam.Token,"");
                    Intent intent = new Intent(SettingActivity.this, LoginActivity.class);
                    // 添加标志
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    startActivity(intent);
                    finish();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 返回当前程序版本名
     */
    public String getAppVersionName() {
        String versionName = "";
        try {
            // ---get the package info---
            PackageManager pm = getPackageManager();
            PackageInfo pi = pm.getPackageInfo(getPackageName(), 0);
            versionName = pi.versionName;
            if (versionName == null || versionName.length() <= 0) {
                return "";
            }
        } catch (Exception e) {
            Logger.e("VersionInfo,Exception" + e);
        }
        return versionName;
    }

    /**
     * 返回当前code
     */
    public int getAppVersionCode() {
        int versionName = 0;
        try {
            // ---get the package info---
            PackageManager pm = getPackageManager();
            PackageInfo pi = pm.getPackageInfo(getPackageName(), 0);
            versionName = pi.versionCode;

        } catch (Exception e) {
            Logger.e("VersionInfo,Exception" + e);
        }
        return versionName;
    }


    /**
     * app升级方法
     */
    private void update() {
        //检查更新
        try {
            //6.0才用动态权限
            if (Build.VERSION.SDK_INT >= 23) {
                String[] permissions = {
                        Manifest.permission.READ_EXTERNAL_STORAGE,
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.ACCESS_WIFI_STATE,
                        Manifest.permission.INTERNET};
                List<String> permissionList = new ArrayList<>();
                for (int i = 0; i < permissions.length; i++) {
                    if (ActivityCompat.checkSelfPermission(this, permissions[i]) != PackageManager.PERMISSION_GRANTED) {
                        permissionList.add(permissions[i]);
                    }
                }
                if (permissionList.size() == 0) {
                    //说明权限都已经通过，可以做你想做的事情去
                    //自动更新
//                    String url  = "http://jokesimg.cretinzp.com/apk/app-release_231_jiagu_sign.apk";
                    AutoUpdater manager = new AutoUpdater(appUrl,SettingActivity.this);
                    manager.CheckUpdate();
                } else {
                    //存在未允许的权限
                    ActivityCompat.requestPermissions(this, permissions, 100);
                }
            }
        } catch (Exception ex) {
            Toast.makeText(SettingActivity.this, "自动更新异常：" + ex.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
}
