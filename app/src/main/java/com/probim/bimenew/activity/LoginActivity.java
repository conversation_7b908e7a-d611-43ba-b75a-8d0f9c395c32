package com.probim.bimenew.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.LoginController;
import com.probim.bimenew.dto.LoginDto;
import com.probim.bimenew.dto.SaveDto;
import com.probim.bimenew.model.Company;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.LoginResult;
import com.probim.bimenew.result.UserSessionDto;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.PwdUtlis;
import com.probim.bimenew.utils.SetAliasUtils;
import com.probim.bimenew.utils.Y5SignUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

//@formatter:off

/**
 * Description :登录界面
 * Author : Gary
 * Email : <EMAIL>
 * Date : 2018/7/17/13:22.
 */
public class LoginActivity extends BaseActivity {

    @BindView(R.id.edt_phone)
    EditText edtPhone;
    @BindView(R.id.edt_pwd)
    EditText edtPwd;
    @BindView(R.id.btn_login)
    TextView btnLogin;
    @BindView(R.id.iv_company)
    ImageView ivCompany;
    @BindView(R.id.tv_company)
    TextView tvCompany;
    @BindView(R.id.iv_phone)
    ImageView ivPhone;
    @BindView(R.id.iv_password)
    ImageView ivPass;
    @BindView(R.id.check_password)
    CheckBox checkBoxPassWord;
    @BindView(R.id.cb_save_pwd)
    CheckBox cbSavePwd;
    @BindView(R.id.cb_serves)
    CheckBox cbServes;
    private boolean isAccountEmpty = true;
    private boolean isPwdEmpty = true;
    private LoginController loginController;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        ButterKnife.bind(this);
        initView();
    }

    @Override
    protected void loadData() {

    }

    @Override
    protected void initView() {
        cbSavePwd.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    //如果选中，保存密码
                } else {

                }
            }
        });
        cbServes.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean b) {
                if (b) {
                    //如果选中，保存密码
                } else {

                }
            }
        });


        checkBoxPassWord.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                if (isChecked) {
                    //如果选中，显示密码
                    edtPwd.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
                    //光标移动输入文本最后
                    edtPwd.setSelection(edtPwd.getText().length());
                } else {
                    //否则隐藏密码
                    edtPwd.setTransformationMethod(PasswordTransformationMethod.getInstance());
                    //光标移动输入文本最后
                    edtPwd.setSelection(edtPwd.getText().length());
                }
            }
        });
        edtPwd.setIncludeFontPadding(true);
        edtPhone.setIncludeFontPadding(true);
        edtPhone.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    ivPhone.setImageDrawable(getDrawable(R.mipmap.account_fill));
                } else {
                    ivPhone.setImageDrawable(getDrawable(R.mipmap.account));
                }
            }
        });
        edtPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                isAccountEmpty = TextUtils.isEmpty(s);
                btnLoginCanClick();
            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s.toString())) {
                    if (Hawk.contains(CustomParam.SAVE)) {
                        // 如果记录过账号
                        List<SaveDto> saveDtoList = Hawk.get(CustomParam.SAVE);
                        Logger.t("数组").e(JsonHelper.toJson(saveDtoList));
                        for (SaveDto dto : saveDtoList) {
                            if (dto.getAccount().equals(s.toString())) {
                                edtPwd.setText(PwdUtlis.decodeString(dto.getPassWord()));
                                break;
                            } else {
                                edtPwd.setText("");
                            }
                        }
                    } else {
                        edtPwd.setText("");
                    }
                }
            }
        });
        edtPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                isPwdEmpty = TextUtils.isEmpty(s);
                btnLoginCanClick();
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
        edtPwd.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    ivPass.setImageDrawable(getDrawable(R.mipmap.password_fill));
                } else {
                    ivPass.setImageDrawable(getDrawable(R.mipmap.password));
                }
            }
        });
        if (Hawk.contains(CustomParam.ACCOUNT)) {
            edtPhone.setText(Hawk.get(CustomParam.ACCOUNT));
        }
        getCompany();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void initRecycleview() {

    }

    @Override
    protected void initRefresh() {

    }

    @OnClick({R.id.rl_select_company, R.id.btn_login, R.id.iv_delete_phone, R.id.tv_agreement})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.rl_select_company:
                startActivity(new Intent(this, EdtCompanyCodeActivity.class));
        /*startActivity(new Intent(this, EdtCompanyCodeActivity.class));
        EdtDialog.getInstance().showEdtDialog(LoginActivity.this, new OnDialogConfirmListener() {
          @Override
          public void OnConfirm(String str_code) {
            if (TextUtils.isEmpty(str_code)) {
              Hawk.put(CustomParam.CompanyCode, "000000");
            } else {
              Hawk.put(CustomParam.CompanyCode, str_code);
            }

            getCompany();
            // Logger.t("企业码---》").e(str_code + "----api---" + ApiConstant.Default_Company_Code);
          }
        });*/
                break;
            case R.id.iv_delete_phone:
                // 清楚账号
                if (TextUtils.isEmpty(edtPhone.getText().toString())) {
                    return;
                }
                edtPhone.setText(null);
                break;

            case R.id.btn_login:

                if (!TextUtils.isEmpty(edtPhone.getText().toString()) && !TextUtils.isEmpty(edtPwd.getText().toString())) {

                    //login(edtPhone.getText().toString(), edtPwd.getText().toString());
                    if (cbServes.isChecked()){
                        login2(edtPhone.getText().toString(), edtPwd.getText().toString());
                    }else {
                        showMsg("请确认同意隐私政策协议");
                    }
                } else {

                    showMsg("账号或密码不能为空");
                }
                break;
            case R.id.tv_agreement:
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.WebAgreemrntUrl, ApiConstant.AGREEMENT);
                Intent intent = new Intent(this, BaseWebActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
                break;

     /* case R.id.iv_change_ip:
        EdtIpDialog.getInstance().showEdtDialog(this, new EdtIpDialog.OnDialogConfirmListener() {
          @Override public void OnConfirm(String str_code) {
            if (TextUtils.isEmpty(str_code)) {
              showMsg("输入IP不合法!");
              return;
            }
            Hawk.put(CustomParam.CompanyIP, str_code);
          }
        });
        break;*/

            default:
                break;
        }
    }

    /**
     * 登陆2
     */
    private void login2(String phone, String pwd) {
        mLoading.show();
        loginController = new LoginController();
   /* HashMap<String, String> params = new HashMap<>();
    params.put("UserName", phone);
    params.put("Password", Y5SignUtil.AESPKS7Encrypt(pwd));*/
        LoginDto loginDto = new LoginDto();
        loginDto.setUserName(phone);
        loginDto.setPassword(Y5SignUtil.AESPKS7Encrypt(pwd + ApiConstant.DES_KEY));
        loginController.Login2(JsonHelper.toJson(loginDto), new CallBack<LoginResult>() {
            @Override
            public void onSuccess(LoginResult loginResult) {

                if (ApiConstant.CODE_TYPE_SUCCESS == loginResult.getRet()) {
                    showMsg("登陆成功");
                    //Hawk.put(CustomParam.ACCOUNT, loginModel.getResultData().getAccount());
                    Hawk.put(CustomParam.IsLogin, true);
                    Hawk.put(CustomParam.Token, loginResult.getData().getToken());
                    //Hawk.put(CustomParam.RealName, loginModel.getResultData().getRealName());
                    //Hawk.put(CustomParam.UserId, loginModel.getResultData().getUserId());
                    Hawk.put(CustomParam.Alpha, loginResult.getData().getAlpha());
                    Hawk.put(CustomParam.LOGINACCOUNT, phone);
                    Hawk.put(CustomParam.LOGINPWD, PwdUtlis.encryptionString(pwd));
                    if (cbSavePwd.isChecked()) {
                        if (Hawk.contains(CustomParam.SAVE)) {
                            // 如果记录过账号
                            List<SaveDto> saveDtoList = Hawk.get(CustomParam.SAVE);
                            for (SaveDto dto : saveDtoList) {
                                if (dto.getAccount().equals(phone)) {
                                    dto.setPassWord(PwdUtlis.encryptionString(pwd));
                                    break;
                                }
                            }
                            SaveDto saveDto = new SaveDto(phone, PwdUtlis.encryptionString(pwd));
                            saveDtoList.add(saveDto);
                            Hawk.put(CustomParam.SAVE, saveDtoList);
                        } else {
                            // 如果未记录过账号
                            List<SaveDto> saveDtoList = new ArrayList<>();
                            saveDtoList.add(new SaveDto(phone, PwdUtlis.encryptionString(pwd)));
                            Hawk.put(CustomParam.SAVE, saveDtoList);
                        }
                    }
                    getUserSeesion(loginResult.getData().getToken());
                    SetAliasUtils.setAlias(loginResult.getData().getAlpha(), LoginActivity.this);
                    Intent intent = new Intent(LoginActivity.this,MainActivity.class);
                    intent.putExtra("Token",loginResult.getData().getToken());
                    startActivity(intent);
                    finish();
                } else {
                    showMsg(loginResult.getMsg());
                }
                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {
                showMsg(erroMsg);
                mLoading.dismiss();
            }
        });
    }

    /**
     * 获取用户userid
     *
     * @param token
     */
    private void getUserSeesion(String token) {
        HashMap<String, String> params = new HashMap<>();
        params.put("Token", token);
        loginController.getUserSession(params, new CallBack<UserSessionDto>() {

            @Override
            public void onSuccess(UserSessionDto userSessionDto) {
                if (userSessionDto.getRet() == 1) {
                    Hawk.put(CustomParam.UserId, userSessionDto.getData().getApisessionUserid());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

    }

    /**
     * 获取企业信息
     */

    private void getCompany() {

        HashMap<String, String> params = new HashMap<>();
        params.put("Code", Hawk.get(CustomParam.CompanyCode));
        LoginController controller = new LoginController();
        controller.GetCompany(params, new CallBack<Company>() {
            @Override
            public void onSuccess(Company company) {

                if (company.getRet() == 1) {
                    for (Company.DataDTO data : company.getData()) {
                        switch (data.getCu_urltype().toLowerCase()) {
                            case "logurl":
                                Glide.with(getApplicationContext()).load(data.getCu_url()).into(ivCompany);
                                break;
                            case "docapi":
                                Hawk.put(CustomParam.Doc_URL, data.getCu_url());
                                break;
                            case "companyname":
                                tvCompany.setText(data.getCu_url());
                                Hawk.put(CustomParam.CompanyStr, data.getCu_url());
                                break;
                            case "modelapi":
                                Hawk.put(CustomParam.Bim_URL, data.getCu_url());
                                break;
                            case "newapi":
                                Hawk.put(CustomParam.Base_URL, data.getCu_url());
                                break;
                            case "newweb":
                                Hawk.put(CustomParam.Web_URL, data.getCu_url());
                                break;
                            case "taskapi":
                                Hawk.put(CustomParam.Schedual_URL, data.getCu_url());
                                break;
                            case "web":
                                handleJson(data);
                                break;
                            case "panorama":
                                Hawk.put(CustomParam.Panorama_URL, data.getCu_url());
                                break;
                            case "mobiletaskapi":
                                Hawk.put(CustomParam.Flow_URL, data.getCu_url());
                                break;
                        }


                    }

                } else {
                    showMsg("输入企业编码有误");
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }
    /**
     * 处理数据
     * @param dataDTO
     */
    private void handleJson(Company.DataDTO dataDTO){
        try {
            if (dataDTO.getCu_desc().startsWith("{") && dataDTO.getCu_desc().endsWith("}")){
                JSONObject jsonObject = new JSONObject(dataDTO.getCu_desc().replace("\\",""));
                if (jsonObject.has("CompanyName")){
                    tvCompany.setText(jsonObject.getString("CompanyName"));
                    Hawk.put(CustomParam.CompanyStr, jsonObject.getString("CompanyName"));
                }
            }

        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
//        CompanyName
    }
    /**
     * 判断登录按钮点击
     */
    private boolean isCanLoginClick() {

        return !isAccountEmpty && !isPwdEmpty;
    }

    private void btnLoginCanClick() {

        if (isCanLoginClick()) {
            btnLogin.setBackground(getDrawable(R.drawable.shape_blue_6));
        } else {
            btnLogin.setBackground(getDrawable(R.drawable.shape_gray_6));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        String compantStr = Hawk.get(CustomParam.CompanyStr);
        tvCompany.setText(compantStr);
    }
}