package com.probim.bimenew.adapter;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.probim.bimenew.R;
import com.probim.bimenew.result.CheckTypeResult;
import com.probim.bimenew.utils.view.fontview.FontTextView;

import java.util.ArrayList;
import java.util.List;

public class CheckTypeAdapter extends RecyclerView.Adapter<CheckTypeAdapter.VHolder> {
    private final Context mContex;
    private final LayoutInflater layoutInflater;
    // 所有点击集合
    private List<Boolean> isTapList = new ArrayList<>();
    private List<CheckTypeResult.DataBean.ListBean> dataBeanList = new ArrayList<>();
    private setOnItemClickListener listener;

    public CheckTypeAdapter(Context mContex, List<CheckTypeResult.DataBean.ListBean> dataBeanList) {
        this.mContex = mContex;
        this.dataBeanList = dataBeanList;
        layoutInflater = LayoutInflater.from(mContex);


    }

    @NonNull
    @Override
    public VHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new VHolder(layoutInflater.inflate(R.layout.item_check_filter, null, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
        CheckTypeResult.DataBean.ListBean data = dataBeanList.get(i);
        vHolder.fontTextView.setText(data.getAedtName());
        vHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {

                    //每次点击 初始化数据
                    for (int j = 0; j < isTapList.size(); j++) {
                        isTapList.set(j, false);

                    }
                    isTapList.set(i, true);
                    notifyDataSetChanged();
                    listener.onItemClick(i);

                }
            }
        });


        if (isTapList.get(i)) {
            //点击的时候是选中状态时 取消选中
            // isTapList.set(i, false);
            vHolder.fontTextView.setBackground(mContex.getDrawable(R.drawable.shape_blue_0));
            vHolder.fontTextView.setTextColor(Color.parseColor("#ffffff"));

        } else {
            //点击的时候是未选中状态时 设置选中
            //isTapList.set(i, true);
            vHolder.fontTextView.setBackground(mContex.getDrawable(R.drawable.shape_gray_0));
            vHolder.fontTextView.setTextColor(mContex.getColor(R.color.BIMe_Blue));


        }

    }


    @Override
    public int getItemCount() {
        return dataBeanList.size();
    }

    public void setOnItemClickListener(setOnItemClickListener onItemClickListener) {
        this.listener = onItemClickListener;
    }

    public void setTapListData(List<Boolean> booleanList) {

        this.isTapList = booleanList;
    }

    public interface setOnItemClickListener {

        void onItemClick(int pos);
    }

    static class VHolder extends RecyclerView.ViewHolder {
        FontTextView fontTextView;

        public VHolder(@NonNull View itemView) {
            super(itemView);
            fontTextView = itemView.findViewById(R.id.tv_check_filter);
        }
    }
}
