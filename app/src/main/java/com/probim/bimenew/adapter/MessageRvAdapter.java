package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

import com.probim.bimenew.R;
import com.probim.bimenew.adapter.MessageRvAdapter.ViewHolder;
import com.probim.bimenew.result.MessageResult;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :消息适配器 Author : Gary Email  : <EMAIL> Date   :
 * 2018/7/4/11:04.
 */
public class MessageRvAdapter extends Adapter<ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private OnRecycleItemListener mRecycleItemListener;
    // 通过构造器传进来的数据
    private List<MessageResult.DataDTO.ListDTO> mDatas;

    public MessageRvAdapter(Context mContext, List<MessageResult.DataDTO.ListDTO> mDatas) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        //指定 viewHolder 的布局样式，并返回该 viewHolder
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_message, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        // 绑定数据，就是给控件设置值
        MessageResult.DataDTO.ListDTO dto = mDatas.get(position);
        switch (dto.getLogAndMsgType()) {
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 10007:
                holder.tvMessageType.setText("问题追踪");
                break;
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
                holder.tvMessageType.setText("项目文档");
                break;
            case 44:
            case 45:
            case 46:
                holder.tvMessageType.setText("现场数据");
                break;
            case 47:
            case 48:
            case 49:
                holder.tvMessageType.setText("项目流程");
                break;
            case 51:
                holder.tvMessageType.setText("项目动态");
                break;
            default:
                holder.tvMessageType.setText("其他");
                break;
        }
        //holder.tvMessageType.setText(dto.getMm_typezh());
        holder.tvMessageContent.setText(dto.getExtDataContent());
        holder.tvMessageDate.setText(dto.getMm_createdatetimestr());
        //holder.tvMessageDate.setText(dto.getMm_createdatetime());
        if ("0".equals(dto.getIsRead())) {
            holder.ivMessage.setVisibility(View.VISIBLE);
        } else {
            holder.ivMessage.setVisibility(View.GONE);
        }

        //item点击事件
        holder.itemView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);
                }
            }
        });

    /*holder.btnDelete.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {

        if (mRecycleItemListener != null) {

          mRecycleItemListener.OnDelete(holder.getAdapterPosition(), dto);
        }
      }
    });*/
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

        void OnDelete(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.tv_msg_type)
        TextView tvMessageType;
        @BindView(R.id.tv_msg_content)
        TextView tvMessageContent;
        @BindView(R.id.tv_msg_date)
        TextView tvMessageDate;
        @BindView(R.id.iv_message)
        ImageView ivMessage;
        @BindView(R.id.lin_container)
        LinearLayout linContainer;
   /* @BindView(R.id.btnDelete)
    Button btnDelete;*/

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
