package com.probim.bimenew.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.result.ProjectListResult;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :首页项目列表适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/4/11:04.
 */
public class Project2Adapter extends Adapter<Project2Adapter.ViewHolder> {

    // 通过构造器传进来的数据
    private final List<ProjectListResult.DataBean.RowsBean> mDatas;
    private OnRecycleItemListener mRecycleItemListener;

    public Project2Adapter(List<ProjectListResult.DataBean.RowsBean> mDatas) {
        this.mDatas = mDatas;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        //指定 viewHolder 的布局样式，并返回该 viewHolder
        return new ViewHolder(LayoutInflater.from(parent.getContext()).inflate(R.layout.item_rv_project, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        // 绑定数据，就是给控件设置值
        ProjectListResult.DataBean.RowsBean dataBean = mDatas.get(position);

        if (!TextUtils.isEmpty(dataBean.getThumbnail())) {
            Glide.with(holder.ivIcon.getContext())
                    .load(dataBean.getThumbnail())
                    .into(holder.ivIcon);
        } else {

            Glide.with(holder.ivIcon.getContext())
                    .load(R.mipmap.img_bg_project_list)
                    .into(holder.ivIcon);
        }
        holder.tvConpany.setText(dataBean.getOrganizeName());
        holder.tvProjectName.setText(dataBean.getProjectName());
        if (!TextUtils.isEmpty(dataBean.getManager())) {
            holder.tvProjectManager.setText(dataBean.getManager());
            holder.tvMangerLetter.setText(dataBean.getManager()
                    .substring(dataBean.getManager().length() - 1));
        } else {
            holder.tvProjectManager.setText("无创建者");
            holder.tvMangerLetter.setText("无");
        }
        if (!TextUtils.isEmpty(dataBean.getExpiryTime())) {

            holder.tvProjectCreateDate.setText(dataBean.getExpiryTime() + "  到期");
        }

        if (!dataBean.getIsPublic()) {

            Glide.with(holder.ivIsPublic.getContext()).load(R.mipmap.unpublic).into(holder.ivIsPublic);
        } else {

            Glide.with(holder.ivIsPublic.getContext()).load(R.mipmap.ispublic).into(holder.ivIsPublic);
        }


       /* holder.itemView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                //点击事件
                mRecycleItemListener.OnRecycleItemClick(view, dataBean);
            }
        });*/
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    @Override
    public void onViewRecycled(ViewHolder holder) {

        if (holder != null) {
            holder.ivIcon.setImageDrawable(null);
        }
        super.onViewRecycled(holder);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int position);
    }

    class ViewHolder extends RecyclerView.ViewHolder implements OnClickListener {

        @BindView(R.id.iv_icon)
        ImageView ivIcon;
        @BindView(R.id.tv_project_name)
        TextView tvProjectName;
        @BindView(R.id.tv_project_manager)
        TextView tvProjectManager;
        @BindView(R.id.iv_isPublic)
        ImageView ivIsPublic;
        @BindView(R.id.iv_isCollect)
        ImageView ivIsCollect;
        @BindView(R.id.tv_project_create_date)
        TextView tvProjectCreateDate;
        @BindView(R.id.tv_company_name)
        TextView tvConpany;
        @BindView(R.id.tv_manger_letter)
        TextView tvMangerLetter;

        public ViewHolder(View itemView) {
            super(itemView);
            ButterKnife.bind(this, itemView);
            itemView.setOnClickListener(this);
        }

        @Override
        public void onClick(View v) {
            if (mRecycleItemListener != null) {
                mRecycleItemListener.OnRecycleItemClick(getAdapterPosition());
            }
        }
    }
}
