package com.probim.bimenew.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import com.probim.bimenew.fragment.BaseFragment;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :首页切换
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/11/16:11.
 */
public class MainTabAdapter extends FragmentStatePagerAdapter {

  private List<BaseFragment> mFragments = new ArrayList<BaseFragment>();

  public MainTabAdapter(List<BaseFragment> fragmentList, FragmentManager fm) {
    super(fm);
    if (fragmentList != null) {
      mFragments = fragmentList;
    }
  }

  @Override
  public Fragment getItem(int position) {
    return mFragments.get(position);
  }

  @Override
  public int getCount() {
    return mFragments.size();
  }


}
