package com.probim.bimenew.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ModelRvAdapter.ViewHolder;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;


/**
 * Description :模型列表页适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/4/11:04.
 */
public class ModelRvAdapter extends Adapter<ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    // 通过构造器传进来的数据
    private final List<ModelSatgeListModel> mDatas;
    private OnRecycleItemListener mRecycleItemListener;


    public ModelRvAdapter(Context mContext, List<ModelSatgeListModel> mDatas) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        //指定 viewHolder 的布局样式，并返回该 viewHolder
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_model, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        // 绑定数据，就是给控件设置值

        ModelSatgeListModel dto = mDatas.get(position);

        if (TextUtils.isEmpty(dto.getThumbnail())) {

            Glide.with(mContext)
                    .load(R.mipmap.img_bg_project_list)
                    .into(holder.ivIcon);

        } else {

            byte[] imageByteArray = Base64.decode(dto.getThumbnail(), Base64.DEFAULT);
            Glide.with(mContext)
                    .load(imageByteArray)
                    .into(holder.ivIcon);

        }
        holder.tvProjectName.setText(dto.getFeatureName());
        holder.tvProjectUpdateTime.setText(dto.getCreateTime().replace("T", " ").split("\\.")[0]);
        //item点击事件
        holder.rlContent.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);

                }
            }
        });
        //按钮1点击事件
        holder.btnShare.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnShare(holder.getAdapterPosition(), dto);
                }
            }
        });
        //按钮2点击事件
        holder.btnDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.onLoad(holder.getAdapterPosition(), dto);

                }

            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    @Override
    public void onViewRecycled(ViewHolder holder) {
        if (holder != null) {
            holder.ivIcon.setImageDrawable(null);
        }
        super.onViewRecycled(holder);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }


    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);


        void onLoad(int pos, T o);

        void OnShare(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.iv_icon)
        ImageView ivIcon;
        @BindView(R.id.tv_project_name)
        TextView tvProjectName;
        @BindView(R.id.tv_project_update_time)
        TextView tvProjectUpdateTime;
        @BindView(R.id.rl_content)
        RelativeLayout rlContent;
        @BindView(R.id.btnShare)
        Button btnShare;
        @BindView(R.id.btnDelete)
        Button btnDelete;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
