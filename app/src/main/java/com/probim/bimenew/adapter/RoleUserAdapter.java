package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.result.RoleUsersDto;

import java.util.List;

public class RoleUserAdapter extends RecyclerView.Adapter<RoleUserAdapter.VHolder> {
    private final List<RoleUsersDto.DataDTO.ListDTO> dtoList;
    private final Context context;
    private final LayoutInflater layoutInflater;
    private OnItemClickListener listener;

    public RoleUserAdapter(List<RoleUsersDto.DataDTO.ListDTO> dtoList, Context context) {
        this.dtoList = dtoList;
        this.context = context;
        layoutInflater = LayoutInflater.from(context);
    }

    @NonNull
    @Override
    public VHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {
        return new VHolder(layoutInflater.inflate(R.layout.item_rv_role_user, viewGroup, false));
    }

    @Override
    public void onBindViewHolder(@NonNull VHolder vHolder, int i) {
        RoleUsersDto.DataDTO.ListDTO dto = dtoList.get(i);
        vHolder.tvRoleName.setText(dto.getRoleName());
        vHolder.tvRoleSize.setText(dto.getUsers().size() + " 人 ");
        vHolder.rlRoleGroup.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onItemClick(i, dto);

                }
            }
        });


        vHolder.ivRoleSelected.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (listener != null) {

                    if (dto.isGroupSelected()) {
                        dto.setGroupSelected(false);
                        Glide.with(context).load(R.mipmap.no_select_circle).into(vHolder.ivRoleSelected);
                    } else {
                        dto.setGroupSelected(true);
                        Glide.with(context).load(R.mipmap.select_circle).into(vHolder.ivRoleSelected);
                    }


                    listener.onGroupClick(i, dto);
                }
            }
        });

        if (dto.isGroupSelected()) {
            Glide.with(context).load(R.mipmap.select_circle).into(vHolder.ivRoleSelected);
        } else {
            Glide.with(context).load(R.mipmap.no_select_circle).into(vHolder.ivRoleSelected);
        }
    }

    @Override
    public int getItemCount() {
        return dtoList.size();
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.listener = onItemClickListener;


    }

    public interface OnItemClickListener {
        void onItemClick(int pos, RoleUsersDto.DataDTO.ListDTO dto);

        void onGroupClick(int pos, RoleUsersDto.DataDTO.ListDTO dto);

    }

    static class VHolder extends RecyclerView.ViewHolder {
        private final TextView tvRoleName;
        private final TextView tvRoleSize;
        private final ImageView ivRoleSelected;
        private final RelativeLayout rlRoleGroup;


        public VHolder(@NonNull View itemView) {
            super(itemView);
            tvRoleName = itemView.findViewById(R.id.tv_role_name);
            tvRoleSize = itemView.findViewById(R.id.tv_role_size);
            ivRoleSelected = itemView.findViewById(R.id.iv_role_selected);
            rlRoleGroup = itemView.findViewById(R.id.rl_role_group);
        }
    }
}
