package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;

import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool;
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation;
import com.bumptech.glide.request.RequestOptions;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.activity.check.FullScreenActivity;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.result.CheckDetailsDto;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.bumptech.glide.load.resource.bitmap.VideoBitmapDecoder.FRAME_OPTION;

/**
 * Description : 多布局文件
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class CheckRecordRvAdapter extends Adapter<CheckRecordRvAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private final int TYPE_TEXT = 0;
    private final int TYPE_IMG = 1;
    private final int TYPE_VIDEO = 2;
    // 通过构造器传进来的数据
    private final List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean> mDatas;
    private final String organizeId;
    private OnRecycleItemListener mRecycleItemListener;

    public CheckRecordRvAdapter(Context mContext,
                                List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean> mDatas,
                                String organizeId) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        this.mLayoutInflater = LayoutInflater.from(mContext);
        this.organizeId = organizeId;
    }

    /**
     * 获取视频第一帧
     *
     * @param videoUrl
     * @return
     */
    public static Bitmap getNetVideoBitmap(String videoUrl) {
        Bitmap bitmap = null;

        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        try {
            //根据url获取缩略图
            retriever.setDataSource(videoUrl, new HashMap());
            //获得第一帧图片
            bitmap = retriever.getFrameAtTime();
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
        return bitmap;
    }

    /**
     * 获取视频第一帧功能
     *
     * @param uri
     * @param imageView
     * @param frameTimeMicros
     */
    public void getVideoScreenshot(String uri, ImageView imageView, long frameTimeMicros) {
        RequestOptions requestOptions = RequestOptions.frameOf(frameTimeMicros);
        requestOptions.set(FRAME_OPTION, MediaMetadataRetriever.OPTION_CLOSEST);
        requestOptions.transform(new BitmapTransformation() {
            @Override
            protected Bitmap transform(@NonNull BitmapPool pool, @NonNull Bitmap toTransform, int outWidth, int outHeight) {
                return toTransform;
            }

            @Override
            public void updateDiskCacheKey(MessageDigest messageDigest) {
                try {
                    messageDigest.update((mContext.getPackageName() + "RotateTransform").getBytes(StandardCharsets.UTF_8));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        Glide.with(mContext).load(uri).apply(requestOptions).into(imageView);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

        View view;
        if (i == TYPE_VIDEO) {
            // 1-3 张图片
            view = mLayoutInflater.inflate(R.layout.item_check_record_video, viewGroup, false);
            return new VideoViewHolder(view);
        } else if (i == TYPE_IMG) {
            //多张图片
            view = mLayoutInflater.inflate(R.layout.item_check_record_more_img, viewGroup, false);
            return new ImageViewMoreHolder(view);
        } else {
            // 文本
            view = mLayoutInflater.inflate(R.layout.item_check_record_text, viewGroup, false);
            return new ViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int i) {

        if (holder instanceof ViewHolder) {
            ViewHolder viewHolder = holder;
            CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(i);

            holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
            holder.tvContent.setText(dto.getRectificationRemark());
            if (dto.getRectificationOperator().length() == 1) {
                holder.tvLetterName.setText(dto.getRectificationOperator());
            } else {
                holder.tvLetterName.setText(dto.getRectificationOperator()
                        .substring(dto.getRectificationOperator().length() - 1
                        ));
            }

            if (dto.getAer_counterpart() != null) {

                switch (dto.getAer_counterpart()) {
                    case "需整改":

                        if (i == mDatas.size() - 1) {
                            holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            holder.tvStatus.setText(dto.getAer_counterpart());
                        } else {
                            holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            holder.tvStatus.setText(dto.getAer_counterpart());
                        }

                        //
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 检查了任务");
                        break;
                    case "验收中":
                        holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_applying);
                        holder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 申请复检");
                        break;
                    case "已合格":
                        holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_ok);
                        holder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 进行了验收");
                        break;
                }
            }
        }
        if (holder instanceof ImageViewMoreHolder) {

            ImageViewMoreHolder imageViewMoreHolder = (ImageViewMoreHolder) holder;
            CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(i);
            imageViewMoreHolder.tvOpertater.setText(dto.getRectificationOperator() + "ddadasdad");
            imageViewMoreHolder.tvDate.setText(dto.getCreateDate().replace("T", " "));
            imageViewMoreHolder.tvContent.setText(dto.getRectificationRemark());
            if (dto.getRectificationOperator().length() == 1) {
                imageViewMoreHolder.tvLetterName.setText(dto.getRectificationOperator());
            } else {
                imageViewMoreHolder.tvLetterName.setText(dto.getRectificationOperator()
                        .substring(dto.getRectificationOperator().length() - 1));
            }

            if (dto.getAer_counterpart() != null) {

                switch (dto.getAer_counterpart()) {
                    case "需整改":

                        if (i == mDatas.size() - 1) {
                            holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            holder.tvStatus.setText(dto.getAer_counterpart());
                        } else {
                            holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            holder.tvStatus.setText(dto.getAer_counterpart());
                        }
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 检查了任务");
                        break;
                    case "验收中":
                        holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_applying);
                        holder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 申请复检");
                        break;
                    case "已合格":
                        holder.tvStatus.setBackgroundResource(R.drawable.bg_tag_ok);
                        holder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 进行了验收");
                        break;
                }
            }
            setItemImage(imageViewMoreHolder, dto.getAttachments());

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (dto.getAttachments().size() != 0) {
                        open(dto.getAttachments());
                    }
                }
            });
        }
        if (holder instanceof VideoViewHolder) {

            VideoViewHolder videoViewHolder = (VideoViewHolder) holder;
            CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(i);
            videoViewHolder.tvOpertater.setText(dto.getRectificationOperator() + "ddadasdad");
            videoViewHolder.tvDate.setText(dto.getCreateDate().replace("T", " "));
            videoViewHolder.tvContent.setText(dto.getRectificationRemark());

            // videoViewHolder.img_video.setImageBitmap(getNetVideoBitmap());
            getVideoScreenshot(Hawk.get(CustomParam.Base_URL) + dto.getAttachments().get(0).getAttachmentUrl(), videoViewHolder.img_video, 0);
            if (dto.getRectificationOperator().length() == 1) {
                videoViewHolder.tvLetterName.setText(dto.getRectificationOperator());
            } else {
                videoViewHolder.tvLetterName.setText(dto.getRectificationOperator()
                        .substring(dto.getRectificationOperator().length() - 1));
            }

            if (dto.getAer_counterpart() != null) {

                switch (dto.getAer_counterpart()) {
                    case "需整改":

                        if (i == mDatas.size() - 1) {
                            videoViewHolder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            videoViewHolder.tvStatus.setText(dto.getAer_counterpart());
                        } else {
                            videoViewHolder.tvStatus.setBackgroundResource(R.drawable.bg_tag_rectification);
                            videoViewHolder.tvStatus.setText(dto.getAer_counterpart());
                        }
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 检查了任务");
                        break;
                    case "验收中":
                        videoViewHolder.tvStatus.setBackgroundResource(R.drawable.bg_tag_applying);
                        videoViewHolder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 申请复检");
                        break;
                    case "已合格":
                        videoViewHolder.tvStatus.setBackgroundResource(R.drawable.bg_tag_ok);
                        videoViewHolder.tvStatus.setText(dto.getAer_counterpart());
                        holder.tvOpertater.setText(dto.getRectificationOperator() + " 进行了验收");
                        break;
                }
            }

            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (dto.getAttachments().size() != 0) {
                        open(dto.getAttachments());
                    }
                }
            });


        }
    }

    /**
     * 打开。。。。。
     */
    private void open(

            List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean> recordWithAttachmentsBeanList) {

        if (isVideoType(recordWithAttachmentsBeanList.get(0).getAttachmentType())) {

            String url = Hawk.get(CustomParam.Base_URL) + recordWithAttachmentsBeanList.get(0)
                    .getAttachmentUrl();
            Intent intent = new Intent(mContext, FullScreenActivity.class);
            intent.putExtra(CustomParam.VIDEOURL, url);
            mContext.startActivity(intent);
        } else {
            //拼接photoUrl
            List<PhotoDto> photoDtoList = new ArrayList<>();
            for (CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean bean : recordWithAttachmentsBeanList) {

                String url = Hawk.get(CustomParam.Base_URL) + bean.getAttachmentUrl();
                photoDtoList.add(new PhotoDto(url));
            }

            Bundle bundle = new Bundle();
            bundle.putSerializable(CustomParam.StartPhoto, (Serializable) photoDtoList);
            bundle.putInt(CustomParam.PhotoPosition, 0);
            Intent intent = new Intent(mContext, PhotoViewActivity.class);
            intent.putExtras(bundle);
            mContext.startActivity(intent);
        }
    }

    @Override
    public int getItemViewType(int position) {
        CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean dto = mDatas.get(position);
        int size = dto.getAttachments().size();

        if (size == 0) {
            return TYPE_TEXT;
        } else {
            if (isVideoType(dto.getAttachments().get(0).getAttachmentType())) {
                return TYPE_VIDEO;

            } else {

                return TYPE_IMG;
            }
        }
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * 获取文件类型
     */
    private boolean isVideoType(String fileType) {

        //选取视频
        return fileType.equalsIgnoreCase(".mp4") || fileType.equalsIgnoreCase(".avi");
    }

    public int dpToPx(float dp) {
        float px = mContext.getResources().getDisplayMetrics().density;
        return (int) (dp * px + 0.5f);
    }

    public void setItemImage(ImageViewMoreHolder viewHolder,
                             List<CheckDetailsDto.DataBean.ListBean.RecordWithAttachmentsBean.AttachmentsBean> list) {
        DisplayMetrics displayMetrics = mContext.getResources().getDisplayMetrics();
        viewHolder.fraAlpha.setVisibility(View.GONE);
        //String url = Hawk.get(CustomParam.Base_URL) + bean.getAttachmentUrl();
        if (list.size() == 1) {

            //图片
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(displayMetrics.widthPixels - dpToPx(10), dpToPx(184)).centerCrop())
                    .into(viewHolder.imgLeft);
            viewHolder.imgRight.setVisibility(View.GONE);
            viewHolder.imgMiddle.setVisibility(View.GONE);
        } else if (list.size() == 2) {
            int imageWidth = (displayMetrics.widthPixels - dpToPx(20)) / 2;
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(184)).centerCrop())
                    .into(viewHolder.imgLeft);
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(1).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(184)).centerCrop())
                    .into(viewHolder.imgMiddle);
            viewHolder.imgRight.setVisibility(View.GONE);
        } else if (list.size() >= 3) {

            viewHolder.fraAlpha.setVisibility(View.VISIBLE);
            viewHolder.tvSize.setText(list.size() + "");
            int imageWidth = (displayMetrics.widthPixels - dpToPx(30)) / 3;

            Glide.with(mContext)
                    .load(R.mipmap.bg_img_size).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)))
                    .into(viewHolder.bgView);
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(0).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(viewHolder.imgLeft);
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(1).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(viewHolder.imgMiddle);
            Glide.with(mContext).load(Hawk.get(CustomParam.Base_URL) + list.get(2).getAttachmentUrl()).apply(RequestOptions.overrideOf(imageWidth, dpToPx(90)).centerCrop())
                    .into(viewHolder.imgRight);
        }
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnPlay(String url);
    }

    class VideoViewHolder extends ViewHolder {

        @BindView(R.id.tv_check_opertater)
        TextView tvOpertater;
        @BindView(R.id.tv_check_letter_name)
        TextView tvLetterName;
        @BindView(R.id.tv_check_content)
        TextView tvContent;
        @BindView(R.id.tv_check_time)
        TextView tvDate;
        @BindView(R.id.tv_check_staus)
        TextView tvStatus;
        @BindView(R.id.img_check_left)
        ImageView img_video;

        public VideoViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    class ImageViewMoreHolder extends ViewHolder {

        @BindView(R.id.tv_check_opertater)
        TextView tvOpertater;
        @BindView(R.id.tv_check_letter_name)
        TextView tvLetterName;
        @BindView(R.id.tv_check_content)
        TextView tvContent;
        @BindView(R.id.img_check_left)
        ImageView imgLeft;
        @BindView(R.id.img_check_middle)
        ImageView imgMiddle;
        @BindView(R.id.img_check_right)
        ImageView imgRight;
        @BindView(R.id.tv_check_time)
        TextView tvDate;
        @BindView(R.id.tv_check_staus)
        TextView tvStatus;
        @BindView(R.id.aplh_view)
        ImageView bgView;
        @BindView(R.id.tv_size)
        TextView tvSize;
        @BindView(R.id.fra_alpha)
        FrameLayout fraAlpha;

        public ImageViewMoreHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.tv_check_opertater)
        TextView tvOpertater;
        @BindView(R.id.tv_check_letter_name)
        TextView tvLetterName;
        @BindView(R.id.tv_check_content)
        TextView tvContent;
        @BindView(R.id.tv_check_time)
        TextView tvDate;
        @BindView(R.id.tv_check_staus)
        TextView tvStatus;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
