package com.probim.bimenew.adapter;

import android.content.Context;
import android.os.AsyncTask;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.DocRvAdapter.OnRecycleItemListener;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.Comments;
import com.probim.bimenew.model.Comments.DataBean;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.StringUtils;
import com.probim.bimenew.utils.view.MyGridView;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/5/17:43.
 */
public class TestCommentsAdapter extends BaseAdapter {

  ViewHolder holder = null;
  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<DataBean> mDatas;
  private String organizeId;

  private List<DocRelevance> DatasList = new ArrayList<>();

  public TestCommentsAdapter(Context mContext,
      List<DataBean> mDatas, String organizeId) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.organizeId = organizeId;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @Override
  public int getCount() {
    return mDatas.size();
  }

  @Override
  public Object getItem(int i) {
    return mDatas.get(i);
  }

  @Override
  public long getItemId(int i) {
    return i;
  }

  @Override
  public View getView(int i, View view, ViewGroup viewGroup) {

    if (view == null) {
      view = mLayoutInflater.inflate(R.layout.item_rv_comments, null);

      holder = new ViewHolder(view);
      view.setTag(holder);
    } else {
      holder = (ViewHolder) view.getTag();
    }

    Comments.DataBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getBU_RealName());
    holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
    holder.tvIntro.setText(dto.getContent());

    //请求文档和照片数据
    List<String> docIdList = new ArrayList<>();

    for (Comments.DataBean.TalkDocsBean talkDocsBean : dto.getTalkDocs()) {
      docIdList.add(talkDocsBean.getTargetID());
    }

    if (!TextUtils.isEmpty(StringUtils.dataToString(docIdList))) {

      new MyTask(StringUtils.dataToString(docIdList), holder.gridPhoto, holder.rvDoc).execute();

      new Thread(new Runnable() {
        @Override
        public void run() {

        }
      }).start();

    }

    return view;
  }

  static class ViewHolder {

    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_intro)
    TextView tvIntro;
    @BindView(R.id.grid_photo)
    MyGridView gridPhoto;
    @BindView(R.id.rv_doc)
    RecyclerView rvDoc;

    ViewHolder(View view) {
      ButterKnife.bind(this, view);
      RecyclerView.LayoutManager manager = new LinearLayoutManager(view.getContext());
      manager.setAutoMeasureEnabled(true);
      rvDoc.setLayoutManager(manager);
      rvDoc.setNestedScrollingEnabled(false);


    }
  }


  class MyTask extends AsyncTask<Void, Void, List<DocRelevance>> {

    private String fileIds;
    private MyGridView gridPhoto;
    private RecyclerView rvDoc;

    public MyTask(String fileIds, MyGridView gridPhoto, RecyclerView rvDoc) {
      this.fileIds = fileIds;
      this.gridPhoto = gridPhoto;
      this.rvDoc = rvDoc;
    }

    @Override
    protected List<DocRelevance> doInBackground(Void... voids) {
      HashMap<String, String> params = new HashMap<>();
      params.put("ProjectID", organizeId);
      params.put("FileIDs", fileIds);
      IssueController issueController = new IssueController();
      Logger.t("参数------------------>").e(params.toString());
      issueController.GetRelevance(params, new CallBack<List<DocRelevance>>() {


        @Override
        public void onSuccess(List<DocRelevance> docRelevances) {
          DatasList = docRelevances;
        }

        @Override
        public void onFail(String erroMsg) {

        }
      });

      return DatasList;
    }

    @Override
    protected void onPostExecute(List<DocRelevance> docRelevances) {
      super.onPostExecute(docRelevances);
      List<PhotoDto> photoDtoList = new ArrayList<>();
      List<DocRelevance> docRelevanceList = new ArrayList<>();
      Logger.t("异步数据------------------>").e(docRelevances.size() + "   ");
      for (DocRelevance docRelevance : docRelevances) {

        String url = null;
        if (".jpeg".equals(docRelevance.getFileExtensions()) || ".png"
            .equals(docRelevance.getFileExtensions()) || ".jpg"
            .equals(docRelevance.getFileExtensions()) || ".PNG"
            .equals(docRelevance.getFileExtensions())) {
          //照片格式

          if (TextUtils.isEmpty(docRelevance.getFilePath())) {
            //隐藏文件
            url = BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + organizeId
                + "&FileId=" + docRelevance
                .getFileId() + "&FileType=Issue";

          } else {
            //不是隐藏文件
            url = BaseApp.getBimUrl() + ApiConstant.LOAD_DOC + "?ProjectID=" + organizeId
                + "&keyValue=" + docRelevance
                .getFileId() + "&FileKind=File";
          }

          photoDtoList
              .add(new PhotoDto(url));


        } else {
          //其余文档
          docRelevanceList.add(docRelevance);

        }

      }

      CommentsPhotoAdapter commentsPhotoAdapter = new CommentsPhotoAdapter(photoDtoList,
          mContext);
      CommentsDocAdapter commentsDocAdapter = new CommentsDocAdapter(mContext,
          docRelevanceList);

      holder.rvDoc.setAdapter(commentsDocAdapter);

      holder.gridPhoto.setAdapter(commentsPhotoAdapter);

    }
  }


}
