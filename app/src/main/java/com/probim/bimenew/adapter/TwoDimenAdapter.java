package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.model.ModelTwoDimensModel;
import com.probim.bimenew.utils.Base64Utils;
import java.util.List;


/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/12/17:14.
 */
public class TwoDimenAdapter extends Adapter<TwoDimenAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  private List<ModelTwoDimensModel.SheetsBean> mDatas;

  public TwoDimenAdapter(Context mContext, List<ModelTwoDimensModel.SheetsBean> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    //指定 viewHolder 的布局样式，并返回该 viewHolder
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_two_dimes, parent, false));
  }


  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    // 绑定数据，就是给控件设置值
    ModelTwoDimensModel.SheetsBean dto = mDatas.get(position);

    Glide.with(mContext)
        .load(Base64Utils.decode(dto.getThumbnail()))
        .into(holder.ivView);
    holder.tvView.setText(dto.getName());
    holder.itemView.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        //点击事件
        mRecycleItemListener.OnRecycleItemClick(view, dto);
      }
    });


  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(View v, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.iv_view)
    ImageView ivView;
    @BindView(R.id.tv_view)
    TextView tvView;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }
}
