package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.QuestionListAdapter.ViewHolder;
import com.probim.bimenew.model.ProblemListModel;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;
import java.util.List;

/**
 * Description :问题列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/7/15:16.
 */
public class QuestionListAdapter extends RecyclerView.Adapter<ViewHolder> {


  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<ProblemListModel.DataBean.ItemsBean> mDatas;

  public QuestionListAdapter(Context mContext,
      List<ProblemListModel.DataBean.ItemsBean> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_question, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    ProblemListModel.DataBean.ItemsBean model = mDatas.get(position);
    holder.tvQuestionName.setText(model.getTitle());
    holder.tvQuestionPeople.setText(model.getCreateUserName());
    holder.tvQuestionStatus.setText(model.getIssueStatusText());
    holder.btnDelete.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnDelete(holder.getAdapterPosition(), model);
        }
      }
    });
    holder.linContainer.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), model);
        }
      }
    });

  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

    void OnDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }


  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.tv_question_name)
    TextView tvQuestionName;
    @BindView(R.id.tv_question_status)
    TextView tvQuestionStatus;
    @BindView(R.id.tv_question_people)
    TextView tvQuestionPeople;
    @BindView(R.id.btnDelete)
    Button btnDelete;
    @BindView(R.id.lin_container)
    LinearLayout linContainer;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }
}
