package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.pm.ApplicationInfo;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.result.IssueDetailResult;
import com.probim.bimenew.utils.FileSizeUtils;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

/**
 * Description :文档列表适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/26/15:11.
 */
public class RelevanceDocAdapter extends RecyclerView.Adapter<RelevanceDocAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private OnRecycleItemListener mRecycleItemListener;
    // 通过构造器传进来的数据
    private List<IssueDetailResult.DataBean.RelationFilesBean> mDatas;

    public RelevanceDocAdapter(Context mContext, List<IssueDetailResult.DataBean.RelationFilesBean> mDatas) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_relevance_doc, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        IssueDetailResult.DataBean.RelationFilesBean dto = mDatas.get(position);
//    Glide.with(mContext).load(dto.getImg()).into(holder.ivImg);
        holder.tvName.setText(dto.getFileName());
        Glide.with(mContext).load(getDrawable("ic_documents_" + dto.getFileName().split("\\.")[1])).into(holder.ivImg);
        holder.linearLayout.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);
                }
            }
        });
        holder.btnDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnDelete(holder.getAdapterPosition(), dto);
                }
            }
        });


    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

        void OnDelete(int pos, T o);
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {

        @BindView(R.id.iv_img)
        ImageView ivImg;
        @BindView(R.id.tv_name)
        TextView tvName;
        @BindView(R.id.tv_date)
        TextView tvDate;
        @BindView(R.id.tv_size)
        TextView tvSize;
        @BindView(R.id.btnDelete)
        Button btnDelete;
        @BindView(R.id.lin_container)
        LinearLayout linearLayout;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    //根据图片名称取得对应图片
    public int getDrawable(String name) {

        ApplicationInfo appInfo = mContext.getApplicationInfo();
        int resID = mContext.getResources().getIdentifier(name, "mipmap", appInfo.packageName);
        //解析资源文件夹下，id为resID的图片
        return resID;

    }
}
