package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.view.ViewGroup;
import com.bumptech.glide.Glide;
import com.github.chrisbanes.photoview.PhotoView;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.utils.Base64Utils;
import java.util.List;

/**
 * Description :照片浏览适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/23/15:36.
 */
public class PhotoPager2Adapter extends PagerAdapter {

  private List<PhotoDto> list;
  private Context context;
  private ViewPager viewPager;
  private IonItemclick listener;

  public PhotoPager2Adapter(List<PhotoDto> list, Context context, ViewPager viewPager) {
    this.list = list;
    this.context = context;
    this.viewPager = viewPager;
  }

  public void setListener(IonItemclick listener) {
    this.listener = listener;
  }

  @Override
  public int getCount() {
    return list.size();
  }

  @Override
  public boolean isViewFromObject(View view, Object object) {
    return view == object;
  }

  @Override
  public Object instantiateItem(final ViewGroup container, int position) {
    final PhotoView view = new PhotoView(context);
    view.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        if (listener != null) {
          listener.ImyClick();
        }
      }
    });

    PhotoDto photoDto = list.get(position);
    if (photoDto != null) {
      Glide.with(context).load(Base64Utils.decode(photoDto.getImg_url())).into(view);
    }
    container.addView(view);
    return view;
  }

  @Override
  public void destroyItem(ViewGroup container, int position, Object object) {
    container.removeView((View) object);
  }


  public interface IonItemclick {

    void ImyClick();
  }

}
