package com.probim.bimenew.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.viewpager.widget.PagerAdapter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Description :无极加载Adapter
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/12/16:49.
 */

public class BasePagerAdapter extends FragmentStatePagerAdapter {

  private List<Fragment> fragmentList;
  private List<String> titleList;

  public BasePagerAdapter(FragmentManager fm, List<Fragment> fragmentList, String[] titles) {
    super(fm);
    this.fragmentList = fragmentList;
    this.titleList = new ArrayList<>(Arrays.asList(titles));
  }

  public BasePagerAdapter(FragmentManager fm, List<Fragment> fragmentList, List<String> titleList) {
    super(fm);
    this.fragmentList = fragmentList;
    this.titleList = titleList;
  }

  @Override
  public Fragment getItem(int position) {
    return fragmentList.get(position);
  }

  @Override
  public int getCount() {
    return titleList.size();
  }

  @Override
  public CharSequence getPageTitle(int position) {
    return titleList.get(position);
  }

  @Override
  public int getItemPosition(Object object) {
    return PagerAdapter.POSITION_NONE;
  }

  public void recreateItems(List<Fragment> fragmentList, List<String> titleList) {
    this.fragmentList = fragmentList;
    this.titleList = titleList;
    notifyDataSetChanged();
  }
}
