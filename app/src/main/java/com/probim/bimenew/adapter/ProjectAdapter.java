package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.dto.ProjectsDto;
import java.util.List;

/**
 * Description :首页项目列表适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/4/11:04.
 */
public class ProjectAdapter extends Adapter<ProjectAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<ProjectsDto.DataBean> mDatas;
  private final Context mContext;

  public ProjectAdapter(Context mContext, List<ProjectsDto.DataBean> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    //指定 viewHolder 的布局样式，并返回该 viewHolder
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_main, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    // 绑定数据，就是给控件设置值
    ProjectsDto.DataBean dataBean = mDatas.get(position);

    if (!TextUtils.isEmpty(dataBean.getThumbnailurl())) {

      Glide.with(mContext)
          .load(dataBean.getThumbnailurl())
          .into(holder.ivIcon);
    } else {

      Glide.with(mContext)
          .load(R.mipmap.img_bg_project_list)
          .into(holder.ivIcon);
    }
    holder.tvProjectName.setText(dataBean.getFullname());
    holder.tvProjectManager.setText(dataBean.getManager());
    holder.tvProjectCreateDate.setText(dataBean.getCreatedate().split(" ")[0]);
    if (0 == dataBean.getIspublic()) {

      holder.tvProjectStatus.setText("非公开");
    } else {

      holder.tvProjectStatus.setText("公开");
    }

    holder.itemView.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        //点击事件
        mRecycleItemListener.OnRecycleItemClick(view, dataBean);
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(View v, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.iv_icon)
    ImageView ivIcon;
    @BindView(R.id.tv_project_name)
    TextView tvProjectName;
    @BindView(R.id.tv_project_manager)
    TextView tvProjectManager;
    @BindView(R.id.tv_project_status)
    TextView tvProjectStatus;
    @BindView(R.id.tv_project_create_date)
    TextView tvProjectCreateDate;

    public ViewHolder(View itemView) {
      super(itemView);
      ButterKnife.bind(this, itemView);
    }
  }
}
