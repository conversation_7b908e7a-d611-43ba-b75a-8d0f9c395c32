package com.probim.bimenew.adapter;

import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.result.CheckListBean;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :现场数据适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/21/15:50.
 */

public class CheckAdapter extends RecyclerView.Adapter<CheckAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final String wait_check = "待检查";
    private final String rectification = "待整改";
    private final String applying = "待验收";
    private final String ok = "已合格";
    private final String closed = "已关闭";
    private final int TYPE_NORMARL = 0;
    private final int TYPE_IMG = 1;
    private final int TYPE_VIDEO = 2;
    private final Context mContext;
    private final List<CheckListBean.DataBean.ListBean> mDatas;
    private OnRecycleItemListener mRecycleItemListener;

    public CheckAdapter(Context mContext, List<CheckListBean.DataBean.ListBean> mdata) {
        this.mDatas = mdata;
        this.mContext = mContext;
        mLayoutInflater = LayoutInflater.from(mContext);
    }

/*
    @Override
    public int getItemViewType(int position) {
        CheckListBean.DataBean.ListBean dto = mDatas.get(position);
        int size = dto.getAttachments().size();

        if (size == 0) {
            return TYPE_NORMARL;
        } else {
            if (UIUtils.isVideoType(dto.getAttachments().get(0).getAttachmentType())) {
                return TYPE_VIDEO;

            } else {

                return TYPE_IMG;
            }
        }
    }*/

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        if (viewType == TYPE_VIDEO) {
            // 1-3 张图片
            view = mLayoutInflater.inflate(R.layout.item_check_video_layou, parent, false);
            return new VideoViewHolder(view);
        } else if (viewType == TYPE_IMG) {
            //多张图片
            view = mLayoutInflater.inflate(R.layout.item_check_img_layout, parent, false);
            return new ImageViewMoreHolder(view);
        } else {
            // 文本
            view = mLayoutInflater.inflate(R.layout.item_check_normarl_layout, parent, false);
            return new ViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder viewHolder, int i) {
        CheckListBean.DataBean.ListBean dto = mDatas.get(i);
        if (!TextUtils.isEmpty(dto.getCreateDate())) {
            String creaTiemStr = "于 " + dto.getCreateDate().split("T")[0] + " 发起";
            viewHolder.tvCheckTime.setText(creaTiemStr);
        }
        StringBuilder strBlank = new StringBuilder("\u3000\u3000");
        for (int j = 0; j < dto.getAedt_name().length() - 1; j++) {
            String str = "\u3000";
            strBlank.append(str);
        }
        viewHolder.tvCheckTitle.setText(strBlank.toString() + dto.getExamineRemark());
        viewHolder.tvCheckType.setText(dto.getAedt_name());
        viewHolder.tvCheckName.setText(dto.getBu_checker_RealName());
        viewHolder.tvStartTime.setText(dto.getCreateDate().split(" ")[0]);
        //holder.tvTotalTime.setText("两天");
        viewHolder.tvEndTime.setText(dto.getRectificateDate().split(" ")[0]);
        if (!TextUtils.isEmpty(dto.getBu_checker_RealName())) {
            if (dto.getBu_checker_RealName().length() == 1) {
                viewHolder.tvCheckLetterName.setText(dto.getBu_checker_RealName());
            } else {
                viewHolder.tvCheckLetterName.setText(dto.getBu_checker_RealName()
                        .substring(dto.getBu_checker_RealName().length() - 1));
            }
        }

        viewHolder.tvCreateName.setText(dto.getBu_examiner_name());

        if (!dto.getCacheRecordList().isEmpty() && dto.getExamineResult().equals("B_ToBeRectified")) {
            //整改人 记录
            viewHolder.linCheck.setVisibility(View.VISIBLE);
            switch (dto.getAede_severitylevel()) {
                case "非常严重":
                    viewHolder.tvCheckClass.setTextColor(Color.parseColor("#F41515"));
                    viewHolder.tvCheckClass.setBackgroundColor(Color.parseColor("#EFDFDE"));
                    viewHolder.tvCheckClass.setText("非常\n严重");

                    break;
                case "严重":
                    viewHolder.tvCheckClass.setTextColor(Color.parseColor("#FF7700"));
                    viewHolder.tvCheckClass.setBackgroundColor(Color.parseColor("#F3E8DD"));
                    viewHolder.tvCheckClass.setText(dto.getAede_severitylevel());

                    break;
                case "一般":
                    viewHolder.tvCheckClass.setTextColor(Color.parseColor("#007AFF"));
                    viewHolder.tvCheckClass.setBackgroundColor(Color.parseColor("#DDE8F5"));
                    viewHolder.tvCheckClass.setText(dto.getAede_severitylevel());

                    break;
            }
            viewHolder.tvCheckConten.setText(dto.getCacheRecordList()
                    .get(dto.getCacheRecordList().size() - 1)
                    .getRectificationRemark());
        } else {

            viewHolder.linCheck.setVisibility(View.GONE);
        }

        switch (dto.getExamineResult()) {
            case "A_ToBeCheck":
                viewHolder.tvCheckStatus.setVisibility(View.VISIBLE);
                viewHolder.tvCheckStatus.setText(wait_check);
                viewHolder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_waitcheck));
                viewHolder.btnClose.setVisibility(View.VISIBLE);
                break;
            case "B_ToBeRectified":
                viewHolder.tvCheckStatus.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(dto.getRelationMemberName())) {
                    viewHolder.tvCheckStatus.setText(dto.getRelationMemberName() + "\u2000" + rectification);
                } else {
                    viewHolder.tvCheckStatus.setText(rectification);
                }

                viewHolder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_rectification));
                viewHolder.btnClose.setVisibility(View.VISIBLE);
                break;
            case "C_ToBeRecheck":
                viewHolder.tvCheckStatus.setVisibility(View.VISIBLE);
                if (!TextUtils.isEmpty(dto.getPrincipalName())) {
                    viewHolder.tvCheckStatus.setText(dto.getPrincipalName() + "\u2000" + applying);
                } else {
                    viewHolder.tvCheckStatus.setText(applying);
                }

                viewHolder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_applying));
                viewHolder.btnClose.setVisibility(View.VISIBLE);
                break;
            case "D_Qualified":
                viewHolder.tvCheckStatus.setText(ok);
                viewHolder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_ok));
                viewHolder.btnClose.setVisibility(View.VISIBLE);
                break;
            case "E_Closed":
                viewHolder.tvCheckStatus.setText(closed);
                viewHolder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_closed));
                viewHolder.btnClose.setVisibility(View.GONE);
                break;
            default:
                break;
        }
        viewHolder.linContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mRecycleItemListener != null) {
                    mRecycleItemListener.OnRecycleItemClick(i, dto);
                }
            }
        });


        viewHolder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) viewHolder.itemView).quickClose();
                    mRecycleItemListener.OnDelete(viewHolder.getAdapterPosition(), dto);
                }
            }
        });
        viewHolder.btnClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) viewHolder.itemView).quickClose();
                    mRecycleItemListener.OnClose(viewHolder.getAdapterPosition(), dto);
                }
            }
        });

    }


    @Override
    public int getItemCount() {
        return mDatas == null ? 0 : mDatas.size();
    }

    public void addAll(List<CheckListBean.DataBean.ListBean> cells) {
        if (cells == null || cells.size() == 0) {
            return;
        }
        mDatas.addAll(cells);
        notifyItemRangeChanged(mDatas.size() - cells.size(), mDatas.size());
    }

    public void addAll(int index, List<CheckListBean.DataBean.ListBean> cells) {
        if (cells == null || cells.size() == 0) {
            return;
        }
        mDatas.addAll(index, cells);
        notifyItemRangeChanged(index, index + cells.size());
    }

    public void clear() {
        mDatas.clear();
        notifyDataSetChanged();
    }

    public List<CheckListBean.DataBean.ListBean> getData() {
        return mDatas;
    }

    public void setData(List<CheckListBean.DataBean.ListBean> data) {
        addAll(data);
        notifyDataSetChanged();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

        void OnDelete(int pos, T o);

        void OnClose(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_check_name)
        TextView tvCheckName;
        @BindView(R.id.tv_check_time)
        TextView tvCheckTime;
        @BindView(R.id.tv_check_staus)
        TextView tvCheckStatus;
        @BindView(R.id.tv_check_type)
        TextView tvCheckType;
        @BindView(R.id.tv_check_title)
        TextView tvCheckTitle;
        @BindView(R.id.tv_check_class)
        TextView tvCheckClass;
        @BindView(R.id.tv_check_content)
        TextView tvCheckConten;
        @BindView(R.id.tv_check_letter_name)
        TextView tvCheckLetterName;
        @BindView(R.id.lin_check)
        LinearLayout linCheck;
        @BindView(R.id.tv_create_name)
        TextView tvCreateName;
        @BindView(R.id.tv_end_time)
        TextView tvEndTime;
        @BindView(R.id.tv_start_time)
        TextView tvStartTime;
        @BindView(R.id.tv_total_time)
        TextView tvTotalTime;
        @BindView(R.id.lin_container)
        LinearLayout linContainer;
        @BindView(R.id.btnDelete)
        Button btnDelete;
        @BindView(R.id.btnClose)
        Button btnClose;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    static class ImageViewMoreHolder extends ViewHolder {
        @BindView(R.id.tv_check_name)
        TextView tvCheckName;
        @BindView(R.id.tv_check_time)
        TextView tvCheckTime;
        @BindView(R.id.tv_check_staus)
        TextView tvCheckStatus;
        @BindView(R.id.tv_check_type)
        TextView tvCheckType;
        @BindView(R.id.tv_check_title)
        TextView tvCheckTitle;
        @BindView(R.id.tv_check_class)
        TextView tvCheckClass;
        @BindView(R.id.tv_check_content)
        TextView tvCheckConten;
        @BindView(R.id.tv_check_letter_name)
        TextView tvCheckLetterName;
        @BindView(R.id.lin_check)
        LinearLayout linCheck;
        @BindView(R.id.tv_create_name)
        TextView tvCreateName;
        @BindView(R.id.tv_end_time)
        TextView tvEndTime;
        @BindView(R.id.tv_start_time)
        TextView tvStartTime;
        @BindView(R.id.tv_total_time)
        TextView tvTotalTime;

        ImageViewMoreHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    static class VideoViewHolder extends ViewHolder {
        @BindView(R.id.tv_check_name)
        TextView tvCheckName;
        @BindView(R.id.tv_check_time)
        TextView tvCheckTime;
        @BindView(R.id.tv_check_staus)
        TextView tvCheckStatus;
        @BindView(R.id.tv_check_type)
        TextView tvCheckType;
        @BindView(R.id.tv_check_title)
        TextView tvCheckTitle;
        @BindView(R.id.tv_check_class)
        TextView tvCheckClass;
        @BindView(R.id.tv_check_content)
        TextView tvCheckConten;
        @BindView(R.id.tv_check_letter_name)
        TextView tvCheckLetterName;
        @BindView(R.id.lin_check)
        LinearLayout linCheck;
        @BindView(R.id.tv_create_name)
        TextView tvCreateName;
        @BindView(R.id.tv_end_time)
        TextView tvEndTime;
        @BindView(R.id.tv_start_time)
        TextView tvStartTime;
        @BindView(R.id.tv_total_time)
        TextView tvTotalTime;

        VideoViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
