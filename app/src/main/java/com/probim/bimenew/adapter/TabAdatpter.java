package com.probim.bimenew.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import com.probim.bimenew.fragment.BaseFragment;
import java.util.List;

/**
 * Description :首页切换
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/6/11/16:11.
 */

public class TabAdatpter extends FragmentPagerAdapter {

  List<BaseFragment> fragments;
  List<String> list;

  public TabAdatpter(FragmentManager fm, List<BaseFragment> fragments,List<String> list) {
    super(fm);
    this.fragments = fragments;
    this.list = list;
  }

  public TabAdatpter(FragmentManager fm) {
    super(fm);
  }

  @Override
  public Fragment getItem(int position) {
    return fragments.get(position);
  }

  @Override
  public int getCount() {
    return fragments == null ? 0 : fragments.size();
  }

  @Override
  public CharSequence getPageTitle(int position) {
    return list.get(position);
  }
}
