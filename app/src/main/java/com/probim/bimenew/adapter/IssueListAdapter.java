package com.probim.bimenew.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import butterknife.BindView;
import butterknife.ButterKnife;

import com.bumptech.glide.Glide;
import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.model.ProblemListModel;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

/**
 * Description :问题列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/7/15:16.
 */
public class IssueListAdapter extends RecyclerView.Adapter<IssueListAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private final Context mContext;
    private OnRecycleItemListener mRecycleItemListener;
    // 通过构造器传进来的数据
    private List<ProblemListModel.DataBean.ItemsBean> mDatas;
    private String mProjectId;

    public IssueListAdapter(Context mContext,
                            List<ProblemListModel.DataBean.ItemsBean> mDatas, String projectId) {
        this.mContext = mContext;
        this.mDatas = mDatas;
        this.mLayoutInflater = LayoutInflater.from(mContext);
        this.mProjectId = projectId;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {

        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_question2, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ProblemListModel.DataBean.ItemsBean model = mDatas.get(position);
        holder.tvQuestionTitle.setText(model.getTitle());
        holder.tvCreateName.setText(model.getCreateUserName());
        holder.tvQuestionCreateDate.setText(model.getCreateDate().split(" ")[0]);

    /*if (TextUtils.isEmpty(model.getFileId())) {

      Glide.with(BaseApp.getContext())
          .load(R.mipmap.img_defult)
          .into(holder.ivIcon);

    } else {

      Glide.with(BaseApp.getContext())
          .load(BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + mProjectId
              + "&FileId=" + model
              .getFileId() + "&FileType=Issue")
          .into(holder.ivIcon);

     *//* Logger.t("图片地址").e(BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + mProjectId
          + "&FileId=" + model
          .getFileId() + "&FileType=Issue");*//*
    }*/

        if (!TextUtils.isEmpty(model.getBgpictureSrc())) {

            Glide.with(BaseApp.getContext())
                    .load(model.getBgpictureSrc())
                    .into(holder.ivIcon);
        } else {
            if (!model.getBaseFiles().isEmpty()) {
                Glide.with(BaseApp.getContext())
                        .load(Hawk.get(CustomParam.Base_URL) + "/" + model.getBaseFiles().get(0).getBfPath())
                        .into(holder.ivIcon);
            } else {
                Glide.with(BaseApp.getContext())
                        .load(R.mipmap.img_defult)
                        .into(holder.ivIcon);
            }

        }


        holder.btnDelete.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnDelete(holder.getAdapterPosition(), model);
                }
            }
        });
        holder.linContainer.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {
                    ((SwipeMenuLayout) holder.itemView).quickClose();
                    mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), model);
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);

        void OnDelete(int pos, T o);
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    static
    class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.iv_icon)
        ImageView ivIcon;
        @BindView(R.id.tv_question_title)
        TextView tvQuestionTitle;
        @BindView(R.id.next_image)
        ImageView nextImage;
        @BindView(R.id.tv_create_name)
        TextView tvCreateName;
        @BindView(R.id.tv_question_create_date)
        TextView tvQuestionCreateDate;
        @BindView(R.id.lin_container)
        LinearLayout linContainer;
        @BindView(R.id.btnDelete)
        Button btnDelete;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
