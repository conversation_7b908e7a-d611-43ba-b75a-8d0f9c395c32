package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.probim.bimenew.R;
import com.probim.bimenew.activity.schedule.dto.SchedualListDto;
import com.probim.bimenew.result.AllTaskResult;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class AllProgressAdapter
        extends RecyclerView.Adapter<AllProgressAdapter.ViewHolder> {

    private Context mContext;
    private List<SchedualListDto.DataDTO> mDatas = new ArrayList<>();
    private final LayoutInflater mLayoutInflater;
    private OnRecycleItemListener mRecycleItemListener;
    private List<Boolean> isClicks;

    public AllProgressAdapter(Context mContext, List<SchedualListDto.DataDTO> datas) {
        this.mDatas = datas;
        this.mContext = mContext;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_all_material, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        SchedualListDto.DataDTO
                bean = mDatas.get(position);
        holder.tvName.setText(bean.getName());
        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    mRecycleItemListener.OnRecycleItemClick(position, bean.getChildren());
                }
            }
        });
    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_material)
        TextView tvName;
        @BindView(R.id.iv_next)
        ImageView ivNext;
        @BindView(R.id.ivSelected)
        ImageView ivSelected;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }
}
