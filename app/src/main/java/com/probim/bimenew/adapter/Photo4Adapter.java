package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.dto.PhotoDto;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :问题详情页面照片展示
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/13/15:42.
 */
public class Photo4Adapter extends RecyclerView.Adapter<Photo4Adapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private List<PhotoDto> mDatas = new ArrayList<>();
  private OnItemListener mItemListener;

  public Photo4Adapter(List<PhotoDto> list, Context mContext) {
    this.mDatas = list;
    this.mContext = mContext;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_photo4, null, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

    PhotoDto dto = mDatas.get(position);
    Glide.with(mContext).load(dto.getImg_url()).into(holder.imgPhoto);
    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mItemListener != null) {
          mItemListener.OnItemClick(position, dto);
        }
      }
    });
    holder.imgDelete.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mItemListener != null) {
          mItemListener.OnItemDelete(position, dto);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.img_photo)
    ImageView imgPhoto;
    @BindView(R.id.img_delete)
    ImageView imgDelete;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnItemListener<T> {

    void OnItemDelete(int pos, T o);

    void OnItemClick(int pos, T o);
  }

  public void addItemListener(OnItemListener onItemListener) {

    this.mItemListener = onItemListener;
  }

 /* PhotoDto dto = mList.get(i);
    Glide.with(mContext).load(dto.getImg_url()).into(holder.imgPhoto);
    holder.imgDelete.setOnClickListener(new OnClickListener() {
    @Override
    public void onClick(View view) {
      if (mItemListener != null) {
        mItemListener.OnDelete(i, dto);
      }
    }
  });*/
}