package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Environment;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import android.view.View;
import android.view.ViewGroup;

import com.bumptech.glide.Glide;
import com.github.chrisbanes.photoview.PhotoView;
import com.probim.bimenew.dto.PhotoDto;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * Description :照片浏览适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/8/23/15:36.
 */
public class PhotoPagerAdapter extends PagerAdapter {

  private List<PhotoDto> list;
  private Context context;
  private ViewPager viewPager;
  private IonItemclick listener;

  public PhotoPagerAdapter(List<PhotoDto> list, Context context, ViewPager viewPager) {
    this.list = list;
    this.context = context;
    this.viewPager = viewPager;
  }

  public void setListener(IonItemclick listener) {
    this.listener = listener;
  }

  @Override
  public int getCount() {
    return list.size();
  }

  @Override
  public boolean isViewFromObject(View view, Object object) {
    return view == object;
  }

  @Override
  public Object instantiateItem(final ViewGroup container, int position) {
    final PhotoView view = new PhotoView(context);
    view.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        if (listener != null) {
          listener.ImyClick();
        }
      }
    });

  /*  view.setOnLongClickListener(new View.OnLongClickListener() {
      @Override
      public boolean onLongClick(View view) {

        new AlertDialogUtil(context).builder().setMsg("保存图片").setNegativeButton("保存",
            new View.OnClickListener() {
              @Override public void onClick(View view) {

                Toast.makeText(context, "保存图片中，请稍后", Toast.LENGTH_SHORT).show();
                final PhotoDto imagesBean = list.get(viewPager.getCurrentItem());
                ThreadUtils.runOnWorkThread(new Runnable() {
                  @Override
                  public void run() {
                    Bitmap path = null;
                    try {
                      path = Glide.with(context)
                          .load(imagesBean.getImg_url())
                          .asBitmap()
                          .into(Target.SIZE_ORIGINAL, Target.SIZE_ORIGINAL)
                          .get();
                      saveImageToGallery(context, path);
                      ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                          Toast.makeText(context, "保存图片成功", Toast.LENGTH_SHORT).show();
                        }
                      });
                    } catch (InterruptedException e) {
                      e.printStackTrace();
                    } catch (ExecutionException e) {
                      e.printStackTrace();
                    }
                  }
                });
              }
            }).setPositiveButton("取消", new View.OnClickListener() {
          @Override public void onClick(View view) {

          }
        }).show();

        return true;
      }
    });*/

    PhotoDto photoDto = list.get(position);
    if (photoDto != null) {
      Glide.with(context).load(photoDto.getImg_url()).into(view);
    }
    container.addView(view);
    return view;
  }

  @Override
  public void destroyItem(ViewGroup container, int position, Object object) {
    container.removeView((View) object);
  }

  public interface IonItemclick {

    void ImyClick();
  }

  public void saveImageToGallery(Context context, Bitmap bmp) {
    // 首先保存图片
    File file = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        .getAbsoluteFile();//注意小米手机必须这样获得public绝对路径
    String fileName = "BIMeFile";
    File appDir = new File(file, fileName);
    if (!appDir.exists()) {
      appDir.mkdirs();
    }
    String name = System.currentTimeMillis() + ".jpg";
    File currentFile = new File(appDir, name);

    FileOutputStream fos = null;
    BufferedOutputStream bos = null;
    try {
      fos = new FileOutputStream(currentFile);
      bos = new BufferedOutputStream(fos);
      bmp.compress(Bitmap.CompressFormat.JPEG, 100, bos);
      bos.flush();
    } catch (FileNotFoundException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    } finally {
      try {
        if (fos != null) {
          bos.close();
          fos.close();
        }
      } catch (IOException e) {
        e.printStackTrace();
      }
    }

    // 其次把文件插入到系统图库
    //        try {
    //            MediaStore.Images.Media.insertImage(context.getContentResolver(),
    //                    currentFile.getAbsolutePath(), fileName, null);
    //        } catch (FileNotFoundException e) {
    //            e.printStackTrace();
    //        }

    // 最后通知图库更新
    context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
        Uri.fromFile(new File(currentFile.getPath()))));
  }
}
