package com.probim.bimenew.adapter;

import android.content.Context;
import android.graphics.Color;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.probim.bimenew.R;
import com.probim.bimenew.result.CheckListBean;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :现场数据适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/21/15:50.
 */

public class CheckAdapter2 extends RecyclerView.Adapter<CheckAdapter2.ViewHolder> {

  private Context mContext;
  private List<CheckListBean.DataBean.ListBean> mDatas;
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  private final String wait_check = "待检查";
  private final String rectification = "待整改";
  private final String applying = "待验收";
  private final String ok = "已合格";

  public CheckAdapter2(Context mContext, List<CheckListBean.DataBean.ListBean> mdata) {
    this.mDatas = mdata;
    this.mContext = mContext;
    mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_check_normarl_layout, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    CheckListBean.DataBean.ListBean dto = mDatas.get(position);
    String creaTiemStr = "于 " + dto.getCreateDate().split("T")[0] + " 发起";
    holder.tvCheckTime.setText(creaTiemStr);
    StringBuilder strBlank = new StringBuilder("\u3000\u3000");
    for (int i = 0; i < dto.getAedt_name().length()-1; i++) {
      String str = "\u3000";
      strBlank.append(str);
    }
    holder.tvCheckTitle.setText(strBlank.toString() + dto.getExamineRemark());
    holder.tvCheckType.setText(dto.getAedt_name());
    holder.tvCheckName.setText(dto.getBu_checker_RealName());
    holder.tvStartTime.setText(dto.getExamineDate().split("T")[0]);
    //holder.tvTotalTime.setText("两天");
    holder.tvEndTime.setText(dto.getRectificateDate().split("T")[0]);
    if (dto.getBu_checker_RealName().length() == 1) {
      holder.tvCheckLetterName.setText(dto.getBu_checker_RealName());
    } else {
      holder.tvCheckLetterName.setText(dto.getBu_checker_RealName()
          .substring(dto.getBu_checker_RealName().length() - 1));
    }
    holder.tvCreateName.setText(dto.getBu_examiner_name());

    if (!dto.getCacheRecordList().isEmpty() && dto.getExamineResult().equals("B_ToBeRectified")) {
      //整改人 记录
      holder.linCheck.setVisibility(View.VISIBLE);
      switch (dto.getAede_severitylevel()) {
        case "非常严重":
          holder.tvCheckClass.setTextColor(Color.parseColor("#F41515"));
          holder.tvCheckClass.setBackgroundColor(Color.parseColor("#EFDFDE"));
          holder.tvCheckClass.setText("非常\n严重");

          break;
        case "严重":
          holder.tvCheckClass.setTextColor(Color.parseColor("#FF7700"));
          holder.tvCheckClass.setBackgroundColor(Color.parseColor("#F3E8DD"));
          holder.tvCheckClass.setText(dto.getAede_severitylevel());

          break;
        case "一般":
          holder.tvCheckClass.setTextColor(Color.parseColor("#007AFF"));
          holder.tvCheckClass.setBackgroundColor(Color.parseColor("#DDE8F5"));
          holder.tvCheckClass.setText(dto.getAede_severitylevel());

          break;
      }
      holder.tvCheckConten.setText(dto.getCacheRecordList()
          .get(dto.getCacheRecordList().size() - 1)
          .getRectificationRemark());
    } else {

      holder.linCheck.setVisibility(View.GONE);
    }

    holder.tvCheckStatus.setText(dto.getExamineResult());
    switch (dto.getExamineResult()) {
      case "A_ToBeCheck":
        holder.tvCheckStatus.setText(wait_check);
        holder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_waitcheck));
        break;
      case "B_ToBeRectified":
        holder.tvCheckStatus.setText(rectification);
        holder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_rectification));
        break;
      case "C_ToBeRecheck":
        holder.tvCheckStatus.setText(applying);
        holder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_applying));
        break;
      case "D_Qualified":
        holder.tvCheckStatus.setText(ok);
        holder.tvCheckStatus.setBackground(mContext.getDrawable(R.drawable.bg_tag_ok));
        break;
      default:
        break;
    }
    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View v) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnRecycleItemClick(position, dto);
        }
      }
    });


    /*holder.btnDelete.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnDelete(holder.getAdapterPosition(), dto);
        }
      }
    });
    holder.linContainer.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);
        }
      }
    });
  }*/
  }

  @Override public int getItemCount() {
    return mDatas == null ? 0 : mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_check_name) TextView tvCheckName;
    @BindView(R.id.tv_check_time) TextView tvCheckTime;
    @BindView(R.id.tv_check_staus) TextView tvCheckStatus;
    @BindView(R.id.tv_check_type) TextView tvCheckType;
    @BindView(R.id.tv_check_title) TextView tvCheckTitle;
    @BindView(R.id.tv_check_class) TextView tvCheckClass;
    @BindView(R.id.tv_check_content) TextView tvCheckConten;
    @BindView(R.id.tv_check_letter_name) TextView tvCheckLetterName;
    @BindView(R.id.lin_check) LinearLayout linCheck;
    @BindView(R.id.tv_create_name) TextView tvCreateName;
    @BindView(R.id.tv_end_time) TextView tvEndTime;
    @BindView(R.id.tv_start_time) TextView tvStartTime;
    @BindView(R.id.tv_total_time) TextView tvTotalTime;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  public void addAll(List<CheckListBean.DataBean.ListBean> cells) {
    if (cells == null || cells.size() == 0) {
      return;
    }
    mDatas.addAll(cells);
    notifyItemRangeChanged(mDatas.size() - cells.size(), mDatas.size());
  }

  public void addAll(int index, List<CheckListBean.DataBean.ListBean> cells) {
    if (cells == null || cells.size() == 0) {
      return;
    }
    mDatas.addAll(index, cells);
    notifyItemRangeChanged(index, index + cells.size());
  }

  public void setData(List<CheckListBean.DataBean.ListBean> data) {
    addAll(data);
    notifyDataSetChanged();
  }

  public void clear() {
    mDatas.clear();
    notifyDataSetChanged();
  }

  public List<CheckListBean.DataBean.ListBean> getData() {
    return mDatas;
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

    void OnDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
