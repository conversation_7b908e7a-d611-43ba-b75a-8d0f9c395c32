package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.result.CheckListResult;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;
import java.util.List;

/**
 * Description :问题检查适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/11/21/15:50.
 */

public class CheckAdapterBack extends RecyclerView.Adapter<CheckAdapterBack.ViewHolder> {

  private Context mContext;
  private List<CheckListResult.DataBean.ListBean> mDatas;
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;

  public CheckAdapterBack(Context mContext, List<CheckListResult.DataBean.ListBean> mdata) {
    this.mDatas = mdata;
    this.mContext = mContext;
    mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_check, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    CheckListResult.DataBean.ListBean dto = mDatas.get(position);
    holder.tvItemName.setText(dto.getExamineRemark());
    holder.tvStructer.setText(dto.getEc_name());
    //holder.tvStructer.setText(dto.getEx);
    /*holder.tvItemIntro.setText(
        TextUtils.isEmpty(dto.getConstructUnit()) ? "<无单位工程>" : dto.getConstructUnit());

    holder.tvItemFenbu.setText(
        TextUtils.isEmpty(dto.getSubConstructUnit()) ? "无" : dto.getSubConstructUnit());

    holder.tvItemFenxiang.setText(
        TextUtils.isEmpty(dto.getSubdivisional()) ? "无" : dto.getSubdivisional());

    holder.tvItemJianyanpi.setText(
        TextUtils.isEmpty(dto.getVerifyBatch()) ? "无" : dto.getVerifyBatch());*/

    holder.tvItemDate.setText(TextUtils.isEmpty(dto.getExamineDate()) ? "数据丢失"
        : dto.getExamineDate().split("T")[0]);
    holder.tvItemType.setText(dto.getExamineResult());
    holder.btnDelete.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnDelete(holder.getAdapterPosition(), dto);
        }
      }
    });
    holder.linContainer.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas == null ? 0 : mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_item_name) TextView tvItemName;
    @BindView(R.id.tv_item_intro) TextView tvItemIntro;
    @BindView(R.id.tv_item_date) TextView tvItemDate;
    @BindView(R.id.tv_item_type) TextView tvItemType;
    @BindView(R.id.btnDelete) Button btnDelete;
    @BindView(R.id.lin_container) LinearLayout linContainer;
    @BindView(R.id.tv_item_fenbu) TextView tvItemFenbu;
    @BindView(R.id.tv_item_fenxiang) TextView tvItemFenxiang;
    @BindView(R.id.tv_item_jianyanpi) TextView tvItemJianyanpi;
    @BindView(R.id.tv_item_structer) TextView tvStructer;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  public void addAll(List<CheckListResult.DataBean.ListBean> cells) {
    if (cells == null || cells.size() == 0) {
      return;
    }
    mDatas.addAll(cells);
    notifyItemRangeChanged(mDatas.size() - cells.size(), mDatas.size());
  }

  public void addAll(int index, List<CheckListResult.DataBean.ListBean> cells) {
    if (cells == null || cells.size() == 0) {
      return;
    }
    mDatas.addAll(index, cells);
    notifyItemRangeChanged(index, index + cells.size());
  }

  public void setData(List<CheckListResult.DataBean.ListBean> data) {
    addAll(data);
    notifyDataSetChanged();
  }

  public void clear() {
    mDatas.clear();
    notifyDataSetChanged();
  }

  public List<CheckListResult.DataBean.ListBean> getData() {
    return mDatas;
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

    void OnDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
