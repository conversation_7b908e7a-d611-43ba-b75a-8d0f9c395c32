package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import com.probim.bimenew.R;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2019/9/16/15:36.
 */

public class CustomTabAdapter extends FragmentPagerAdapter {

  private List<Fragment> mFragmentList = new ArrayList<>();

  private Context context;

  private String[] titles;

  private int[] icon;

  public CustomTabAdapter(Context context, FragmentManager fm, List<Fragment> mFragmentList,
      String[] titles, int[] icon) {
    super(fm);
    this.mFragmentList = mFragmentList;
    this.icon = icon;
    this.titles = titles;
    this.context = context;
  }

  @Override public Fragment getItem(int i) {
    return mFragmentList.get(i);
  }

  @Override public int getCount() {
    return mFragmentList.size();
  }

  //注意！！！这里就是我们自定义的布局tab_item
  public View getCustomView(int position) {
    View view = LayoutInflater.from(context).inflate(R.layout.bottom_tab_item_red_layout, null);
    ImageView iv = (ImageView) view.findViewById(R.id.iv_icon);
    TextView tv = (TextView) view.findViewById(R.id.tv_title);
   // TextView tvRed = view.findViewById(R.id.tv_red);
    iv.setImageDrawable(context.getResources().getDrawable(icon[position]));
    tv.setText(titles[position]);
   /* switch (position) {
      case 0:
        //drawable代码在文章最后贴出
        iv.setImageDrawable(context.getResources().getDrawable(icon[0]));
        tv.setText("首页");
        break;
      case 1:
        iv.setImageDrawable(
            context.getResources().getDrawable(icon[1]);
        tv.setText("课程表");
        break;
      case 2:
        iv.setImageDrawable(context.getResources().getDrawable(icon[2]));
        tv.setText("我的提问");
        break;
      case 3:
        iv.setImageDrawable(context.getResources().getDrawable(R.drawable.tab_mine_selector));
        tv.setText("我的");
        break;
    }*/
    return view;
  }
}
