package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.model.DocRelevance;
import com.probim.bimenew.utils.FileSizeUtils;
import java.util.List;

/**
 * Description :文档列表适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/26/15:11.
 */
public class CommentsDocAdapter extends RecyclerView.Adapter<CommentsDocAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  // 通过构造器传进来的数据
  private List<DocRelevance> mDatas;
  private OnRecycleItemListener mRecycleItemListener;

  public CommentsDocAdapter(Context mContext, List<DocRelevance> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_comments_doc, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    holder.setIsRecyclable(false);
    DocRelevance dto = mDatas.get(position);
//    Glide.with(mContext).load(dto.getImg()).into(holder.ivImg);
    holder.tvDate.setText(dto.getModifyDate());
    holder.tvName.setText(dto.getFileName());

    if ("0".equals(dto.getFileSize())) {
      //大小等于0  文件夹
      holder.tvSize.setText("");
      Glide.with(mContext).load(R.mipmap.ic_documents_folder).into(holder.ivImg);
    } else {
      //不等于0  文件
      holder.tvSize.setText(FileSizeUtils.getReadableFileSize(Long.parseLong(dto.getFileSize())));

      if (getDrawable("ic_documents_" + dto.getFileExtensions().replace(".", "")) == 0) {
        Glide.with(mContext).load(
            R.mipmap.ic_documents_no)
            .into(holder.ivImg);
      } else {
        Glide.with(mContext).load(
            getDrawable("ic_documents_" + dto.getFileExtensions().replace(".", "")))
            .into(holder.ivImg);
      }
    }
    holder.itemView.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnRecycleItemClick(position, dto);
        }
      }
    });


  }


  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.iv_img)
    ImageView ivImg;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_size)
    TextView tvSize;
    @BindView(R.id.lin_container)
    LinearLayout linearLayout;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  //根据图片名称取得对应图片
  public int getDrawable(String name) {

    ApplicationInfo appInfo = mContext.getApplicationInfo();
    int resID = mContext.getResources().getIdentifier(name, "mipmap", appInfo.packageName);
    //解析资源文件夹下，id为resID的图片
    return resID;

  }


  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);


  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
