package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.model.DocFileDto;
import com.probim.bimenew.model.DocFirstFloor;
import com.probim.bimenew.utils.FileSizeUtils;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :文档列表适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/26/15:11.
 */
public class DocRvAdapter extends RecyclerView.Adapter<DocRvAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<DocFileDto.DataDTO> mDatas;

  public DocRvAdapter(Context mContext, List<DocFileDto.DataDTO> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_doc, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    DocFileDto.DataDTO dto = mDatas.get(position);
    //    Glide.with(mContext).load(dto.getImg()).into(holder.ivImg);
    holder.tvDate.setText(dto.getCreateTime());
    holder.tvName.setText(dto.getFileName());

    if (dto.getIsFolder()) {
      //大小等于0  文件夹
      holder.tvSize.setText("");
      Glide.with(mContext).load(R.mipmap.folder).into(holder.ivImg);
      holder.btnShare.setVisibility(View.GONE);
    } else {
     // holder.btnShare.setVisibility(View.VISIBLE);
      //不等于0  文件
      holder.tvSize.setText(dto.getFileSize()+dto.getSizeUnit());
      String imageType = dto.getFileExtension().replace(".", "").toLowerCase();
      String mipmapName = null;
      switch (imageType) {
        case "png":
        case "jpg":
        case "jpeg":
          mipmapName = "image";
          break;
        case "pptx":
        case "ppt":
          mipmapName = "ppt";
          break;
        case "doc":
        case "docx":
          mipmapName = "word";
          break;
        case "xls":
        case "xlsx":
          mipmapName = "excel";
          break;
        case "dwg":
          mipmapName = "dwg";
          break;
        case "txt":
          mipmapName = "txt";
          break;
        case "pdf":
          mipmapName = "pdf";
          break;
        case "zip":
          mipmapName = "archive";
          break;
        case "mp4":
          mipmapName = "video";
          break;
        default:
          mipmapName = "unknown";
          break;
      }
      setImageFromMipmap(mipmapName, holder.ivImg);
    }

    holder.linearLayout.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          Logger.t("doc-log").e(position+"--------大小"+ mDatas.size());
          Logger.t("doc-log").e(new Gson().toJson(dto));
          mRecycleItemListener.OnRecycleItemClick(position, dto);
        }
      }
    });
    holder.btnDelete.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnDelete(position, dto);
        }
      }
    });

    holder.btnShare.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();

          if (!"0".equals(dto.getFileSize())) {

            //大小等于0  文件夹
            mRecycleItemListener.onShare(position, dto);
          }
        }
      }
    });
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

    void onShare(int pos, T o);

    void OnDelete(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.iv_img)
    ImageView ivImg;
    @BindView(R.id.tv_name)
    TextView tvName;
    @BindView(R.id.tv_date)
    TextView tvDate;
    @BindView(R.id.tv_size)
    TextView tvSize;
    @BindView(R.id.btnShare)
    Button btnShare;
    @BindView(R.id.btnDelete)
    Button btnDelete;
    @BindView(R.id.lin_container)
    LinearLayout linearLayout;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  //根据图片名称取得对应图片
  public int getDrawable(String name) {

    ApplicationInfo appInfo = mContext.getApplicationInfo();
    int resID = mContext.getResources().getIdentifier(name, "mipmap", appInfo.packageName);
    //解析资源文件夹下，id为resID的图片
    return resID;
  }

  //根据文件类型加载本地图片
  public void setImageFromMipmap(String name, ImageView imageView) {

    Glide.with(mContext).load(
        getDrawable(name))
        .into(imageView);
  }
}
