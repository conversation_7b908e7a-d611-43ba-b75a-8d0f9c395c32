package com.probim.bimenew.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.result.AllTaskItemResult;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class TaskItemAdapter
        extends RecyclerView.Adapter<TaskItemAdapter.ViewHolder> {

    private final LayoutInflater mLayoutInflater;
    private Context mContext;
    private List<AllTaskItemResult.DataBean.TasksBean> mDatas = new ArrayList<>();
    private OnRecycleItemListener mRecycleItemListener;
    private boolean isFromTask;

    public TaskItemAdapter(Context mContext, boolean isFromTask, List<AllTaskItemResult.DataBean.TasksBean> datas) {
        this.mDatas = datas;
        this.mContext = mContext;
        this.isFromTask = isFromTask;
        this.mLayoutInflater = LayoutInflater.from(mContext);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_item_task, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllTaskItemResult.DataBean.TasksBean
                bean = mDatas.get(position);

        holder.tvName.setText(bean.getNAME_());
        //holder.tvCode.setText(bean.getBm_materialcode());
        if (bean.isSelected()) {
            Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
        } else {
            Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
        }

        // SUMMARY_ =1有置灰
        if (bean.getSUMMARY_() == 1) {
            holder.ivSelected.setVisibility(View.INVISIBLE);
            holder.ivNext.setVisibility(View.VISIBLE);
        } else {
            holder.ivSelected.setVisibility(View.VISIBLE);
            holder.ivNext.setVisibility(View.INVISIBLE);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mRecycleItemListener != null) {

                    if (bean.isSelected()) {
                        bean.setSelected(false);
                        Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
                    } else {
                        if (isFromTask) {
                            for (int i = 0; i < mDatas.size(); i++) {
                                mDatas.get(i).setSelected(false);
                                notifyDataSetChanged();
                            }
                        }
                        bean.setSelected(true);
                        Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
                    }
                    mRecycleItemListener.OnRecycleItemClick(position, bean);
                }
            }
        });


    }

    @Override
    public int getItemCount() {
        return mDatas.size();
    }

    public void addRecycleItemListener(OnRecycleItemListener listener) {
        this.mRecycleItemListener = listener;
    }

    /**
     * ItemClick的回调接口
     *
     * <AUTHOR>
     */
    public interface OnRecycleItemListener<T> {

        void OnRecycleItemClick(int pos, T o);
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        @BindView(R.id.tv_material)
        TextView tvName;
        @BindView(R.id.tv_code)
        TextView tvCode;
        @BindView(R.id.ivSelected)
        ImageView ivSelected;
        @BindView(R.id.iv_next)
        ImageView ivNext;

        ViewHolder(View view) {
            super(view);
            ButterKnife.bind(this, view);
        }
    }
}
