package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.result.AllMaterialListBean;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class AllMaterialAdapter
    extends RecyclerView.Adapter<AllMaterialAdapter.ViewHolder> {

  private Context mContext;
  private List<AllMaterialListBean.DataBean.ListBean> mDatas = new ArrayList<>();
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  private List<Boolean> isClicks;

  public AllMaterialAdapter(Context mContext, List<AllMaterialListBean.DataBean.ListBean> datas) {
    this.mDatas = datas;
    this.mContext = mContext;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_all_material, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    AllMaterialListBean.DataBean.ListBean
        bean = mDatas.get(position);
    holder.tvName.setText(bean.getBmc_name());
    if (bean.getDirectChildrenCount() == 0 && bean.getChildrenItemCount()==0) {
      holder.ivNext.setVisibility(View.GONE);
    } else {
      holder.ivNext.setVisibility(View.VISIBLE);
    }
    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {

          mRecycleItemListener.OnRecycleItemClick(position, bean);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_material) TextView tvName;
    @BindView(R.id.iv_next) ImageView ivNext;
    @BindView(R.id.ivSelected) ImageView ivSelected;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
