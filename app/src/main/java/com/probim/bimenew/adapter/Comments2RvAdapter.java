package com.probim.bimenew.adapter;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.PhotoViewActivity;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.dto.PhotoDto;
import com.probim.bimenew.model.Comments2;
import com.probim.bimenew.utils.SpacesItemDecoration;
import com.probim.bimenew.utils.view.MyGridView;
import com.probim.bimenew.utils.view.NoScrollRecyclerview;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class Comments2RvAdapter extends Adapter<Comments2RvAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<Comments2.DataBean> mDatas;
  private String organizeId;

  public Comments2RvAdapter(Context mContext, List<Comments2.DataBean> mDatas, String organizeId) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
    this.organizeId = organizeId;
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_comments2, null, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int i) {

    Comments2.DataBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getBU_RealName());
    holder.tvDate.setText(dto.getCreateDate().replace("T", " "));
    holder.tvIntro.setText(dto.getContent());
    holder.tvHead.setText(dto.getBU_RealNameLetters());
    //每次初始化列表 保证数据唯一性
    List<PhotoDto> photoDtoList = new ArrayList<>();
    List<Comments2.DataBean.InnerListBean> innerList = new ArrayList<>();
    //照片url拼接
    for (Comments2.DataBean.TalkDocsBean talkDocsBean : dto.getTalkDocs()) {
      //请求文档和照片数据
      String photoUrl = BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + organizeId
          + "&FileId=" + talkDocsBean.getTargetID()
          + "&FileType=Issue";
      photoDtoList.add(new PhotoDto(photoUrl));
    }

    innerList.addAll(dto.getInnerList());
    //判断内部列表数据 为空隐藏掉并回复
    if (innerList.size() == 0) {
      holder.tvReplyNoClick.setVisibility(View.GONE);
    } else {
      holder.tvReplyNoClick.setVisibility(View.VISIBLE);
    }
    loadPhotoData(holder.gridPhoto, photoDtoList);
    loadInnerList(holder.rvCommentsSecond, innerList);
    //照片  内部列表 数据传输
    new Thread(new Runnable() {
      @Override public void run() {

      }
    }).start();

    holder.tvReplyClick.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnItemReply(i, dto.getIssue_TalkId());
        }
      }
    });

    holder.ivDelete.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnItemDelete(i, dto.getIssue_TalkId());
        }
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static
  class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_head) TextView tvHead;
    @BindView(R.id.tv_name) TextView tvName;
    @BindView(R.id.rv_comments_second) NoScrollRecyclerview rvCommentsSecond;
    @BindView(R.id.tv_reply_noClick) TextView tvReplyNoClick;
    @BindView(R.id.tv_intro) TextView tvIntro;
    @BindView(R.id.grid_photo) MyGridView gridPhoto;
    @BindView(R.id.tv_date) TextView tvDate;
    @BindView(R.id.tv_reply_click) TextView tvReplyClick;
    @BindView(R.id.iv_delete) ImageView ivDelete;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
      RecyclerView.LayoutManager manager = new LinearLayoutManager(view.getContext());
      rvCommentsSecond.setLayoutManager(manager);
      rvCommentsSecond.setNestedScrollingEnabled(false);
      rvCommentsSecond.addItemDecoration(new SpacesItemDecoration(30));
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnItemReply(int pos, T o);

    void OnInnerItemReply(int pos, T o);

    void OnItemDelete(int pos, T o);

    void OnInnerItemDelete(int pos, T o);
  }

  public void addRecycleItemListener(Comments2RvAdapter.OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }

  /**
   * 照片适配器
   */
  private void loadPhotoData(MyGridView gridPhoto, List<PhotoDto> list) {

    CommentsPhotoAdapter commentsPhotoAdapter = new CommentsPhotoAdapter(list,
        mContext);

    gridPhoto.setOnItemClickListener(new AdapterView.OnItemClickListener() {
      @Override
      public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        if (list.size() != 0) {
          Bundle bundle = new Bundle();
          bundle.putSerializable(CustomParam.StartPhoto, (Serializable) list);
          bundle.putInt(CustomParam.PhotoPosition, i);
          Intent intent = new Intent(mContext, PhotoViewActivity.class);
          intent.putExtras(bundle);
          mContext.startActivity(intent);
        }
      }
    });

    gridPhoto.setAdapter(commentsPhotoAdapter);
  }

  /**
   * 内部列表数据填充
   */
  private void loadInnerList(RecyclerView innerRy, List<Comments2.DataBean.InnerListBean> list) {
    CommentsInnerRvAdapter commentsInnerRvAdapter = new CommentsInnerRvAdapter(mContext,
        list, organizeId);
    innerRy.setAdapter(commentsInnerRvAdapter);

    commentsInnerRvAdapter.addRecycleItemListener(
        new CommentsInnerRvAdapter.OnRecycleItemListener() {
          @Override public void OnInnerItemReply(int pos, Object o) {
            //内部点击事件
            if (mRecycleItemListener != null) {
              mRecycleItemListener.OnInnerItemReply(pos, list.get(pos).getIssue_TalkId());
            }
          }

          @Override public void OnInnerItemDelete(int pos, Object o) {
            //内部删除事件
            if (mRecycleItemListener != null) {
              mRecycleItemListener.OnInnerItemDelete(pos, list.get(pos).getIssue_TalkId());
            }
          }
        });
  }
}

