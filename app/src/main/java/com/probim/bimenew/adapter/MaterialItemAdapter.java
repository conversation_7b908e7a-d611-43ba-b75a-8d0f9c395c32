package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.R;
import com.probim.bimenew.result.MateriaListBean;
import java.util.ArrayList;
import java.util.List;

/**
 * Description :工程结构适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/12/26/13:58.
 */

public class MaterialItemAdapter
    extends RecyclerView.Adapter<MaterialItemAdapter.ViewHolder> {

  private Context mContext;
  private List<MateriaListBean.DataBean.ListBean> mDatas = new ArrayList<>();
  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  private List<Boolean> isClicks;

  public MaterialItemAdapter(Context mContext, List<MateriaListBean.DataBean.ListBean> datas) {
    this.mDatas = datas;
    this.mContext = mContext;
    this.mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_item_material, parent, false));
  }

  @Override public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    MateriaListBean.DataBean.ListBean
        bean = mDatas.get(position);

    holder.tvName.setText(bean.getBm_materialname());
    holder.tvCode.setText(bean.getBm_materialcode());
    if (bean.getSelected()) {
      Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
    } else {
      Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
    }

    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override public void onClick(View view) {
        if (mRecycleItemListener != null) {

          if (bean.getSelected()) {
            bean.setSelected(false);
            Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
          } else {
            bean.setSelected(true);
            Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
          }

         /* boolean bl = isClicks.get(position);
          if (!bl) {
            isClicks.set(position, true);
            Glide.with(mContext).load(R.mipmap.select_circle).into(holder.ivSelected);
            notifyDataSetChanged();
          } else {
            isClicks.set(position, false);
            Glide.with(mContext).load(R.mipmap.no_select_circle).into(holder.ivSelected);
            notifyDataSetChanged();
          }*/

          mRecycleItemListener.OnRecycleItemClick(position, bean);
        }
      }
    });
  }

  @Override public int getItemCount() {
    return mDatas.size();
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_material) TextView tvName;
    @BindView(R.id.tv_code) TextView tvCode;
    @BindView(R.id.ivSelected) ImageView ivSelected;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}
