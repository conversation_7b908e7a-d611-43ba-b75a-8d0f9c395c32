package com.probim.bimenew.adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.bumptech.glide.Glide;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.R;
import com.probim.bimenew.api.ApiConstant;
import com.probim.bimenew.result.IssueDetailResult;

import java.util.List;

/**
 * Description :
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/9/4/17:20.
 */
public class CommentsAdapter extends Adapter<CommentsAdapter.ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private final Context mContext;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<IssueDetailResult.DataBean.CommentsBean> mDatas;
  private String organizeId;

  public CommentsAdapter(Context mContext, List<IssueDetailResult.DataBean.CommentsBean> mDatas,
      String organizeIdInner) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    this.mLayoutInflater = LayoutInflater.from(mContext);
    this.organizeId = organizeIdInner;
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup viewGroup, int i) {

    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_comments, null, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int i) {

    IssueDetailResult.DataBean.CommentsBean dto = mDatas.get(i);
    holder.tvName.setText(dto.getRealname());
    holder.tvDate.setText(dto.getCreatedate().replace("T", " "));
    String letter =
        dto.getRealname().substring(dto.getRealname().length() - 1, dto.getRealname().length());
    holder.tvIntro.setText(dto.getContent());
    holder.tvHead.setText(letter);
    holder.tvIntro.setText(dto.getContent());

    if (dto.getContenttypeJson().getType().equals("text")) {
      //只是文字
      holder.ivPhoto.setVisibility(View.GONE);
    } else if (dto.getContenttypeJson().getType().equals("image")) {
      //只是图片
      holder.ivPhoto.setVisibility(View.VISIBLE);
      String photoUrl = BaseApp.getBimUrl() + ApiConstant.LOAD_HIDE + "?ProjectID=" + organizeId
          + "&FileId=" + dto.getContenttypeJson().getContentImgId()
          + "&FileType=Issue";
      //只是照片
      Glide.with(mContext).load(photoUrl).into(holder.ivPhoto);
    }

    holder.ivDelete.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          mRecycleItemListener.OnItemDelete(i, dto);
        }
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  static
  class ViewHolder extends RecyclerView.ViewHolder {
    @BindView(R.id.tv_head) TextView tvHead;
    @BindView(R.id.tv_name) TextView tvName;
    @BindView(R.id.tv_intro) TextView tvIntro;
    @BindView(R.id.tv_date) TextView tvDate;
    @BindView(R.id.iv_delete) ImageView ivDelete;
    @BindView(R.id.iv_photo) ImageView ivPhoto;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnItemReply(int pos, T o);

    void OnInnerItemReply(int pos, T o);

    void OnItemDelete(int pos, T o);

    void OnInnerItemDelete(int pos, T o);
  }

  public void addRecycleItemListener(CommentsAdapter.OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }
}

