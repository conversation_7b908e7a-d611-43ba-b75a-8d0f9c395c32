package com.probim.bimenew.adapter;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.Adapter;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import butterknife.BindView;
import butterknife.ButterKnife;
import com.probim.bimenew.R;
import com.probim.bimenew.adapter.ModelViewRvAdapter.ViewHolder;
import com.probim.bimenew.model.ModelViewModel;
import com.probim.bimenew.utils.swipemenu.SwipeMenuLayout;
import java.util.List;


/**
 * Description :模型-->视图  适配器
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/4/11:04.
 */
public class ModelViewRvAdapter extends Adapter<ViewHolder> {

  private final LayoutInflater mLayoutInflater;
  private OnRecycleItemListener mRecycleItemListener;
  // 通过构造器传进来的数据
  private List<ModelViewModel> mDatas;
  private final Context mContext;


  public ModelViewRvAdapter(Context mContext, List<ModelViewModel> mDatas) {
    this.mContext = mContext;
    this.mDatas = mDatas;
    mLayoutInflater = LayoutInflater.from(mContext);
  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    //指定 viewHolder 的布局样式，并返回该 viewHolder
    return new ViewHolder(mLayoutInflater.inflate(R.layout.item_rv_model_view, parent, false));
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    // 绑定数据，就是给控件设置值
    ModelViewModel dto = mDatas.get(position);
    holder.tvViewName.setText(dto.getName());
    holder.tvViewIntro.setText("");
    if (dto.isIsDefault()) {
      holder.ivDefault.setVisibility(View.VISIBLE);
    } else {
      holder.ivDefault.setVisibility(View.INVISIBLE);
    }
    //item点击事件
    holder.linearLayout.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {

          mRecycleItemListener.OnRecycleItemClick(holder.getAdapterPosition(), dto);

        }
      }
    });

    //设为默认
    holder.btnDefalut.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.onSetDefault(holder.getAdapterPosition(), dto);
        }
      }
    });
    //分享视点
    holder.btnShare.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        if (mRecycleItemListener != null) {
          ((SwipeMenuLayout) holder.itemView).quickClose();
          mRecycleItemListener.OnShare(holder.getAdapterPosition(), dto);
        }
      }
    });
  }

  @Override
  public int getItemCount() {
    return mDatas.size();
  }

  /**
   * ItemClick的回调接口
   *
   * <AUTHOR>
   */
  public interface OnRecycleItemListener<T> {

    void OnRecycleItemClick(int pos, T o);

    void onSetDefault(int pos, T o);

    void OnShare(int pos, T o);
  }

  public void addRecycleItemListener(OnRecycleItemListener listener) {
    this.mRecycleItemListener = listener;
  }


  static class ViewHolder extends RecyclerView.ViewHolder {

    @BindView(R.id.tv_view_name)
    TextView tvViewName;
    @BindView(R.id.tv_view_intro)
    TextView tvViewIntro;
    @BindView(R.id.iv_isDefault)
    TextView ivDefault;
    @BindView(R.id.btnDefalut)
    Button btnDefalut;
    @BindView(R.id.btnShare)
    Button btnShare;
    @BindView(R.id.lin_container)
    LinearLayout linearLayout;

    ViewHolder(View view) {
      super(view);
      ButterKnife.bind(this, view);
    }
  }
}
