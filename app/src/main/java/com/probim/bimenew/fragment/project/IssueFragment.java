package com.probim.bimenew.fragment.project;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.Animation.AnimationListener;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.orhanobut.hawk.Hawk;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.IssueDetailActivity;
import com.probim.bimenew.activity.NewIssueActivity;
import com.probim.bimenew.activity.SearchIssueActivity;
import com.probim.bimenew.adapter.IssueListAdapter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.application.BaseApp;
import com.probim.bimenew.controller.IssueController;
import com.probim.bimenew.db.ProjectBeanDao;
import com.probim.bimenew.db.bean.ProjectBean;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.model.ProblemListModel;
import com.probim.bimenew.model.ProblemListModel.DataBean.ItemsBean;
import com.probim.bimenew.model.ProblemStatusModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.result.BaseResult;
import com.probim.bimenew.result.DeleteIssueResult;
import com.probim.bimenew.result.IssueTypeDto;
import com.probim.bimenew.utils.dropdownmenu.bean.DropdownItemObject;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownButton;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownListView;
import com.probim.bimenew.utils.dropdownmenu.view.DropdownListView.Container;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * Description :问题
 * Author : Gary
 * Email : <EMAIL>
 * Date : 2018/7/9/16:59.
 */
public class IssueFragment extends BaseFragment implements Container {

    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.lin_back)
    LinearLayout linBack;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.img_right)
    ImageView imgRight;
    @BindView(R.id.drop_status)
    DropdownButton dropStatus;
    @BindView(R.id.drop_type)
    DropdownButton dropType;
    @BindView(R.id.drop_about)
    DropdownButton dropAbout;
    @BindView(R.id.lin_search)
    LinearLayout linSearch;
    @BindView(R.id.rv_question)
    RecyclerView rvQuestion;
    @BindView(R.id.mask)
    View mask;
    @BindView(R.id.lis_dropdown_status)
    DropdownListView lisDropdownStatus;
    @BindView(R.id.lis_dropdown_type)
    DropdownListView lisDropdownType;
    @BindView(R.id.lis_dropdown_about)
    DropdownListView lisDropdownAbout;
    @BindView(R.id.lin_rv_status_layout)
    LinearLayout linRvStatusLayout;
    @BindView(R.id.iv_result)
    ImageView ivResult;
    @BindView(R.id.tv_result)
    TextView tvResult;
    @BindView(R.id.tv_add)
    TextView tvAdd;
    @BindView(R.id.tv_new)
    TextView tvNew;
    @BindView(R.id.lin_title)
    LinearLayout linTitle;

    private List<ItemsBean> list = new ArrayList<>();
    private IssueListAdapter adapter;
    Unbinder unbinder;
    private List<DropdownItemObject> chooseTypeData = new ArrayList<>();
    private List<DropdownItemObject> chooseAboutData = new ArrayList<>();
    private List<DropdownItemObject> chooseStatusData = new ArrayList<>();
    private DropdownListView currentDropdownList;
    Animation dropdown_in, dropdown_out, dropdown_mask_out;
    private IssueController controller = new IssueController();
    private String organizeId;
    // 定义三个变量
    private String statusSelected = "";
    private String typeSelected = "";
    private String userSelected = "";
    private String filterId;
    private Boolean authorty;
    // 是否有权限

    public static IssueFragment newInstance(String projectId, String projectName, boolean authority,
                                            boolean addAu) {
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.ProblemAuthority, authority);
        args.putBoolean(CustomParam.AddProblemAuthoruty, addAu);
        IssueFragment fragment = new IssueFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_problem, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        GetProjectDao();
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 在视图创建完成后再调用 GetFilter，确保所有视图都已正确绑定
        GetFilter();
    }

    @Override
    protected void initData() {
        linTitle.setVisibility(View.VISIBLE);
        tvTitle.setVisibility(View.VISIBLE);
        tvTitle.setText(getArguments().getString(CustomParam.ProjectName));
        imgRight.setVisibility(View.VISIBLE);
        imgRight.setImageResource(R.mipmap.btn_common_add);
        // 加载动画
        dropdown_in = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_in);
        dropdown_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_out);
        dropdown_mask_out = AnimationUtils.loadAnimation(getActivity(), R.anim.dropdown_mask_out);

        DropdownItemObject aboutBean = new DropdownItemObject("隐藏归档", 0, "隐藏归档");
        DropdownItemObject aboutBean1 = new DropdownItemObject("显示归档", 1, "显示归档");
        chooseAboutData.add(aboutBean);
        chooseAboutData.add(aboutBean1);
        //chooseAboutData.add(aboutBean3);
    }

    @Override
    protected void initRecycleview() {
        // 为 RecyclerView 设置布局管理器
        rvQuestion.setLayoutManager(new LinearLayoutManager(getActivity()));
        // 为 RecyclerView 设置分割线(这个可以对 DividerItemDecoration 进行修改，自定义)
        rvQuestion.addItemDecoration(
                new DividerItemDecoration(getActivity(), DividerItemDecoration.VERTICAL));
        // 动画
        rvQuestion.setItemAnimator(new DefaultItemAnimator());

        adapter = new IssueListAdapter(getActivity(), list, filterId);

        rvQuestion.setAdapter(adapter);

        adapter.addRecycleItemListener(new IssueListAdapter.OnRecycleItemListener() {
            @Override
            public void OnRecycleItemClick(int pos, Object o) {
                Bundle bundle = new Bundle();
                Intent intent = new Intent(getActivity(), IssueDetailActivity.class);
                bundle.putString(CustomParam.IssueId, list.get(pos).getIssueId());
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {
                ItemsBean itemsBean = (ItemsBean) o;
                if (itemsBean.getIsIssueManager()) {
                    DeleteProblem(list.get(pos).getIssueId(), pos);
                } else {
                    showMsg("暂无权限删除问题");
                }
            }
        });
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    void init() {
        // 添加空指针检查，确保视图已经正确绑定
        if (mask == null) {
            // 如果 mask 为空，说明视图还没有准备好，延迟执行
            if (getView() != null) {
                getView().post(new Runnable() {
                    @Override
                    public void run() {
                        init(); // 延迟重新调用 init()
                    }
                });
            }
            return;
        }

        mask.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                hide();
            }
        });
        reset();

        // 添加空指针检查
        if (lisDropdownStatus != null && dropStatus != null) {
            lisDropdownStatus.bind(chooseStatusData, dropStatus, this, 0);
        }

        if (lisDropdownType != null && dropType != null) {
            lisDropdownType.bind(chooseTypeData, dropType, this, 0);
        }

        if (lisDropdownAbout != null && dropAbout != null) {
            lisDropdownAbout.bind(chooseAboutData, dropAbout, this, 0);
        }

        if (dropdown_mask_out != null) {
            dropdown_mask_out.setAnimationListener(new AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {

                }

                @Override
                public void onAnimationEnd(Animation animation) {
                    if (currentDropdownList == null) {
                        reset();
                    }
                }

                @Override
                public void onAnimationRepeat(Animation animation) {

                }
            });
        }
    }

    void reset() {
        if (dropStatus != null) {
            dropStatus.setChecked(false);
        }
        if (dropAbout != null) {
            dropAbout.setChecked(false);
        }
        if (dropType != null) {
            dropType.setChecked(false);
        }

        if (mask != null) {
            mask.setVisibility(View.GONE);
            mask.clearAnimation();
        }
        if (lisDropdownAbout != null) {
            lisDropdownAbout.setVisibility(View.GONE);
            lisDropdownAbout.clearAnimation();
        }
        if (lisDropdownType != null) {
            lisDropdownType.setVisibility(View.GONE);
            lisDropdownType.clearAnimation();
        }
        if (lisDropdownStatus != null) {
            lisDropdownStatus.setVisibility(View.GONE);
            lisDropdownStatus.clearAnimation();
        }
    }

    @Override
    public void show(DropdownListView listView) {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_out != null) {
                currentDropdownList.startAnimation(dropdown_out);
            }
            currentDropdownList.setVisibility(View.GONE);
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(false);
            }
        }
        currentDropdownList = listView;
        if (mask != null) {
            mask.clearAnimation();
            mask.setVisibility(View.VISIBLE);
        }
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_in != null) {
                currentDropdownList.startAnimation(dropdown_in);
            }
            currentDropdownList.setVisibility(View.VISIBLE);
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(true);
            }
        }
    }

    @Override
    public void hide() {
        if (currentDropdownList != null) {
            currentDropdownList.clearAnimation();
            if (dropdown_out != null) {
                currentDropdownList.startAnimation(dropdown_out);
            }
            if (currentDropdownList.button != null) {
                currentDropdownList.button.setChecked(false);
            }
            if (mask != null) {
                mask.clearAnimation();
                if (dropdown_mask_out != null) {
                    mask.startAnimation(dropdown_mask_out);
                }
            }
        }
        currentDropdownList = null;
    }

    @Override
    public void onSelectionChanged(DropdownListView view, String itemId) {
        // 状态筛选
        if (lisDropdownStatus == view) {

            if (itemId.equals("全部状态")) {
                statusSelected = "";
            } else {
                statusSelected = itemId;
            }
            GetProblemList(typeSelected, statusSelected, "", userSelected);

            // 分类筛选
        } else if (lisDropdownType == view) {

            if (itemId.equals("全部分类")) {
                typeSelected = "";
            } else {
                typeSelected = itemId;
            }
            GetProblemList(typeSelected, statusSelected, "", userSelected);
            // 关于筛选
        } else if (lisDropdownAbout == view) {

            if (itemId.equals("隐藏归档")) {
                userSelected = "0";
            } else {
                userSelected = "1";
            }
            GetProblemList(typeSelected, statusSelected, "", userSelected);
        }
    }

    @OnClick({R.id.iv_back, R.id.lin_back, R.id.lin_search, R.id.img_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                getActivity().finish();
                break;
            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.lin_search:
                startActivity(SearchIssueActivity.class);
                break;
            case R.id.img_right:
                Intent intent = new Intent(getActivity(), NewIssueActivity.class);
                startActivity(intent);
                break;
        }
    }

    /**
     * 获取筛选条件
     */
    private void GetFilter() {
        HashMap<String, String> params = new HashMap<>();
        params.put("organizeId", organizeId);
        params.put("token", Hawk.get(CustomParam.Token));
        //params.put()
        controller.getProblemStatus(params, new CallBack<ProblemStatusModel>() {

            @Override
            public void onSuccess(ProblemStatusModel problemStatusModel) {
                if (problemStatusModel.getRet() == 1) {
                    //状态数据
                    DropdownItemObject statusBean = new DropdownItemObject("全部状态", 0, "全部状态");
                    chooseStatusData.add(statusBean);

                    for (int i = 0; i < problemStatusModel.getData().size(); i++) {
                        DropdownItemObject object =
                                new DropdownItemObject(problemStatusModel.getData().get(i).getItemName(),
                                        i + 1, problemStatusModel.getData().get(i).getItemDetailId());
                        chooseStatusData.add(object);
                    }
                    init();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });

        HashMap<String, String> typeParams = new HashMap<>();
        typeParams.put("organizeId", organizeId);
        typeParams.put("token", Hawk.get(CustomParam.Token));
        controller.getProblemType(typeParams, new CallBack<IssueTypeDto>() {

            @Override
            public void onSuccess(IssueTypeDto issueTypeDto) {
                //分类数据
                if (issueTypeDto.getRet() == 1) {
                    DropdownItemObject typeBean = new DropdownItemObject("全部分类", 0, "全部分类");
                    chooseTypeData.add(typeBean);

                    for (int i = 0; i < issueTypeDto.getData().size(); i++) {
                        DropdownItemObject object =
                                new DropdownItemObject(issueTypeDto.getData().get(i).getItemName(),
                                        i + 1, issueTypeDto.getData().get(i).getItemDetailId());
                        chooseTypeData.add(object);
                    }
                    init();
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 获取问题列表
     */
    private void GetProblemList(String typeId, String statusId, String keyWord, String userType) {

        HashMap<String, String> params = new HashMap<>();
        params.put("OrganizeId", filterId);
        params.put("IssueTypeId", typeId);
        params.put("IssueStatusId", statusId);
        params.put("keyword", keyWord);
        params.put("UserType", "");
        params.put("PageIndex", "1");
        params.put("PageSize", Integer.MAX_VALUE + "");
        params.put("Token", Hawk.get(CustomParam.Token));
        params.put("ContainsArchive", userType);
        // Logger.t("问题参数------------》").e(params.toString());
        controller.GetProblemList(params, new CallBack<ProblemListModel>() {
            @Override
            public void onSuccess(ProblemListModel problemListModel) {
                if (problemListModel.getRet() == 1) {
                    if (problemListModel.getData() != null) {
                        list.clear();
                        list.addAll(problemListModel.getData().getItems());
                        adapter.notifyDataSetChanged();

                        if (list.isEmpty()) {
                            rvQuestion.setVisibility(View.GONE);
                            linRvStatusLayout.setVisibility(View.VISIBLE);
                        } else {
                            rvQuestion.setVisibility(View.VISIBLE);
                            linRvStatusLayout.setVisibility(View.GONE);
                        }
                    }
                } else {
                    rvQuestion.setVisibility(View.GONE);
                    linRvStatusLayout.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    /**
     * 从数据库获取数据
     */
    private void GetProjectDao() {
        ProjectBeanDao dao = BaseApp.getInstance().getDao();
        List<ProjectBean> beanList = dao.loadAll();
        for (ProjectBean bean : beanList) {
            organizeId = bean.getBimProjectId();
            filterId = bean.getProjectID();
        }
    }

    /**
     * 删除问题
     */
    private void DeleteProblem(String id, int postion) {
        HashMap<String, String> params = new HashMap<>();
        params.put("IssueId", id);
        params.put("Token", Hawk.get(CustomParam.Token));

        controller.DeleteProblem(params, new CallBack<DeleteIssueResult>() {

            @Override
            public void onSuccess(DeleteIssueResult deleteIssueResult) {
                if (1 == deleteIssueResult.getRet()) {
                    list.remove(postion);
                    adapter.notifyItemRemoved(postion);
                    adapter.notifyItemRangeChanged(postion, list.size());
                }
            }

            @Override
            public void onFail(String erroMsg) {

            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        GetXX();
    }

    // 获取问题列表
    private void GetXX() {
        if (getArguments() != null) {
            String projectName = getArguments().getString(CustomParam.ProjectName);
            authorty = getArguments().getBoolean(CustomParam.ProblemAuthority);
            boolean addNewAu = getArguments().getBoolean(CustomParam.AddProblemAuthoruty);
            tvTitle.setText(projectName);

            //GetFilter();
            GetProblemList("", "", "", "0");
/*
      if (authorty) {



        if (addNewAu) {

          imgRight.setImageResource(R.mipmap.btn_common_add);
        } else {
          imgRight.setOnClickListener(null);
          imgRight.setImageResource(R.mipmap.btn_common_add_dis);
        }
      } else {

        linRvStatusLayout.setVisibility(View.VISIBLE);
        tvResult.setText("暂无权限");
        tvAdd.setText("");
        tvNew.setText("");
        imgRight.setOnClickListener(null);
        imgRight.setImageResource(R.mipmap.btn_common_add_dis);
        linSearch.setOnClickListener(null);
      }*/
        }
    }

    /**
     * 初始化下拉菜单
     */
    private void initDropDown() {
        chooseStatusData.clear();
        chooseTypeData.clear();
        DropdownItemObject statusBean = new DropdownItemObject("状态", 0, "状态");
        chooseStatusData.add(statusBean);

        DropdownItemObject typeBean = new DropdownItemObject("分类", 0, "分类");
        chooseTypeData.add(typeBean);
    }
}
