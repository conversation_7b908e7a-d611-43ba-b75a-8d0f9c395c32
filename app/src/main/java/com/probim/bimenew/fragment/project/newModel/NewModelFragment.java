package com.probim.bimenew.fragment.project.newModel;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.kcrason.dynamicpagerindicatorlibrary.DynamicPagerIndicator;
import com.orhanobut.hawk.Hawk;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.R;
import com.probim.bimenew.activity.LoadModelViewActivity;
import com.probim.bimenew.activity.check.VerticalNoItemRecyclerView;
import com.probim.bimenew.activity.check.VerticalRecyclerView;
import com.probim.bimenew.adapter.TabAdatpter;
import com.probim.bimenew.api.CustomParam;
import com.probim.bimenew.controller.ModelController;
import com.probim.bimenew.dto.ModelStageDto;
import com.probim.bimenew.dto.ModelStageListDto;
import com.probim.bimenew.fragment.BaseFragment;
import com.probim.bimenew.interfaces.IOnItemClickListener;
import com.probim.bimenew.model.ModelSatgeListModel;
import com.probim.bimenew.net.CallBack;
import com.probim.bimenew.utils.JsonHelper;
import com.probim.bimenew.utils.view.NoScrollViewPager;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

/**
 * Description :模型列表
 * Author : Gary
 * Email  : <EMAIL>
 * Date   : 2018/7/9/15:22.
 */
public class NewModelFragment extends BaseFragment {

    Unbinder unbinder;
    @BindView(R.id.tv_left)
    TextView tvLeft;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.img_right)
    ImageView imgRight;
    @BindView(R.id.rv_status_layout)
    RelativeLayout rvStatusLayout;
    @BindView(R.id.tv_status)
    TextView tvStatus;
    @BindView(R.id.lin_all_model)
    LinearLayout linAllModel;
    @BindView(R.id.rv_all_stage)
    RecyclerView rvAllStage;
    @BindView(R.id.rv_all_model)
    RecyclerView rvAllModel;
    @BindView(R.id.tv_right_stage)
    TextView tvStage;

    private List<ModelSatgeListModel> allModels = new ArrayList<>();

    private List<ModelStageDto> allStages = new ArrayList<>();

    private List<ModelStageListDto> allFilterData = new ArrayList<>();

    private List<ModelSatgeListModel> cuurentModels = new ArrayList<>();
    private AllModelAdapter allModelAdapter;

    public static NewModelFragment newInstance(String projectId, String projectName, boolean au, List<ModelStageDto> allStages) {
        NewModelFragment fragment = new NewModelFragment();
        Bundle args = new Bundle();
        args.putString(CustomParam.ProjectId, projectId);
        args.putString(CustomParam.ProjectName, projectName);
        args.putBoolean(CustomParam.ModelAuthority, au);
        args.putSerializable(CustomParam.AllStages, (Serializable) allStages);
        fragment.setArguments(args);
        return fragment;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_new_model, container, false);
        unbinder = ButterKnife.bind(this, view);
        initData();
        initRecycleview();
        return view;
    }

    @Override
    protected void initData() {
        if (getArguments() != null) {
            allStages = (List<ModelStageDto>) getArguments().getSerializable(CustomParam.AllStages);
        }
    }

    @Override
    protected void initRecycleview() {
        allModelAdapter = new AllModelAdapter(cuurentModels);
        allModelAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                ModelSatgeListModel modelSatgeListModel = (ModelSatgeListModel) o;
                Bundle bundle = new Bundle();
                bundle.putString(CustomParam.ModelId, modelSatgeListModel.getFeatureID());
                bundle.putString(CustomParam.ProjectId, organizeId);
                bundle.putString(CustomParam.TvLeft, modelSatgeListModel.getFeatureName());
                Intent intent = new Intent(getActivity(), LoadModelViewActivity.class);
                intent.putExtras(bundle);
                startActivity(intent);
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(rvAllModel).setAdapter(allModelAdapter);
    }

    @Override
    protected void initRefresh() {

    }

    @Override
    protected void loadData() {
        if (getArguments() != null) {
            String projectId = getArguments().getString(CustomParam.ProjectId);
            String projectName = getArguments().getString(CustomParam.ProjectName);
            boolean modelAuthority = getArguments().getBoolean(CustomParam.ModelAuthority);
            tvTitle.setVisibility(View.VISIBLE);
            tvTitle.setText(projectName);
            if (modelAuthority) {
                getAllModels(projectId);
            } else {
                linAllModel.setVisibility(View.GONE);
                rvStatusLayout.setVisibility(View.VISIBLE);
                tvStatus.setText("暂无权限");
            }
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    /**
     * 获取所有模型
     *
     * @param projectId
     */
    private void getAllModels(String projectId) {
        ModelController controller = new ModelController();
        HashMap<String, String> params = new HashMap<>();
        params.put("VaultID", projectId);
        mLoading.show();
        controller.getAllList(params, new CallBack<List<ModelSatgeListModel>>() {
            @Override
            public void onSuccess(List<ModelSatgeListModel> modelSatgeListModels) {
                allModels.addAll(modelSatgeListModels);
                cuurentModels.addAll(allModels);
                allModelAdapter.notifyDataSetChanged();
                tvStage.setText("全部模型");
                Hawk.put(CustomParam.AllModel, allModels);
                if (!allModels.isEmpty()) {
                    handleData();
                }

                mLoading.dismiss();
            }

            @Override
            public void onFail(String erroMsg) {
                mLoading.dismiss();
            }
        });
    }

    /**
     * 处理模型数据
     */
    private void handleData() {

        AllStageAdapter allStageAdapter = new AllStageAdapter(allStages);
        allStageAdapter.setOnItemClickListener(new IOnItemClickListener() {
            @Override
            public void onClick(int pos, Object o) {
                cuurentModels.clear();
                ModelStageDto stageDto = (ModelStageDto) o;
                tvStage.setText(stageDto.getName());
                for (int i = 0; i < stageDto.getData().size(); i++) {
                    if (stageDto.getData().get(i).equals("allmodel_phase")) {
                        cuurentModels.addAll(allModels);

                    } else {

                        for (int j = 0; j < allModels.size(); j++) {
                            if (allModels.get(j).getPhase().equals(stageDto.getData().get(i))) {
                                cuurentModels.add(allModels.get(j));
                            }
                        }
                    }
                }
                allModelAdapter.notifyDataSetChanged();
            }

            @Override
            public void OnDelete(int pos, Object o) {

            }

            @Override
            public void OnClose(int pos, Object o) {

            }
        });
        VerticalNoItemRecyclerView.initialize(rvAllStage).setAdapter(allStageAdapter);
/*
        for (int i = 0; i < allModels.size(); i++) {
            for (int j = 0; j < allStages.size(); j++) {
                ModelStageListDto modelStageListDto = new ModelStageListDto();
                modelStageListDto.setName(allStages.get(j).getCode());
                for (int k = 0; k < allStages.get(j).getData().size(); k++) {
                    if (allModels.get(i).getPhase() == allStages.get(j).getData().get(i)) {
                        modelStageListDto.getData().add(allModels.get(i));
                    }
                }
            }
        }*/
//        Logger.t("allFilterData").e(JsonHelper.toJson(allStages));
    }


    @OnClick({R.id.iv_back, R.id.lin_back, R.id.img_right})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.iv_back:
                getActivity().finish();
                break;
            case R.id.lin_back:
                getActivity().finish();
                break;
            case R.id.img_right:
                showMsg("搜索");
                break;
        }
    }
}
