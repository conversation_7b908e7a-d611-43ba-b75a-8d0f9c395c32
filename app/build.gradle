apply plugin: 'com.android.application'
//GreenDao
apply plugin: 'org.greenrobot.greendao'
apply plugin: 'kotlin-android'
//apply from: 'and_res_guard.gradle'


// Creates a variable called keystorePropertiesFile, and initializes it to the
// keystore.properties file.
def keystorePropertiesFile = rootProject.file("keystore.properties")

// Initializes a new Properties() object called keystoreProperties.
def keystoreProperties = new Properties()

// Loads the keystore.properties file into the keystoreProperties object.
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

android {
  compileSdkVersion 34
  defaultConfig {
    applicationId "com.probim.bimenew"
    minSdkVersion 26
    targetSdkVersion 34
    versionCode 120
    versionName "1.2.0"
    multiDexEnabled true
    testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    ndk {
      //选择要添加的对应 cpu 类型的 .so 库。
      // 设置支持的SO库架构
      //      abiFilters 'armeabi', 'armeabi-v7a'
      abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a','x86'
    }
    ndk {
      moduleName "libteigha_jni"
    }


    manifestPlaceholders = [
            JPUSH_PKGNAME : applicationId,
            JPUSH_APPKEY : "9712e3fb0bcd60afd56b375c", //JPush 上注册的包名对应的 Appkey.
            JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.

    ]
    signingConfigs {

      config {
        keyAlias keystoreProperties['keyAlias']
        keyPassword keystoreProperties['keyPassword']
        storeFile file(keystoreProperties['storeFile'])
        storePassword keystoreProperties['storePassword']
      }
    }
  }

  sourceSets {
    main {
      jni.srcDirs = []
    }
  }
  buildTypes {
    debug {
      //signingConfig signingConfigs.config
      //shrinkResources true
      //minifyEnabled true
      //proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

      //使用buildConfigField用于给BuildConfig文件添加一个常量字段
      buildConfigField "boolean", "LOG_DEBUG", "false"
    }


    release {
      // 不显示Log
      //shrinkResources true
      zipAlignEnabled true
     // minifyEnabled true
      proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
    }
  }
  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  lintOptions {

    checkReleaseBuilds false

    abortOnError false
  }
  buildFeatures {
    viewBinding true
  }

  // 支持 16KB 页面大小的设备
  packagingOptions {
    jniLibs {
      useLegacyPackaging = false
    }
  }

  // 添加 16KB 页面大小支持的测试配置
  testOptions {
    unitTests {
      includeAndroidResources = true
    }
  }

}
greendao {
  schemaVersion 1
  daoPackage 'com.probim.BIMe.db'
  targetGenDir 'src/main/java'
}


dependencies {

  implementation project(':dynamicpagerindicatorlibrary')
  implementation project(':matisse')
  implementation project(':banner')
  implementation project(':beautifylib')
  implementation 'androidx.appcompat:appcompat:1.0.0'
  implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
  implementation 'androidx.recyclerview:recyclerview:1.0.0'
  implementation 'com.google.android.material:material:1.0.0'
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
  implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation files('libs/tbs_sdk_thirdapp_v4.3.0.386_44286_sharewithdownloadwithfile_withoutGame_obfs_20230210_114429.jar')
    testImplementation 'junit:junit:4.12'
  androidTestImplementation 'androidx.test.ext:junit:1.1.1'
  androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
  implementation 'androidx.cardview:cardview:1.0.0'

  implementation fileTree(include: ['*.jar'], dir: 'libs')

  implementation 'org.greenrobot:eventbus:3.1.1'

  //json解析
  implementation 'com.google.code.gson:gson:2.8.6'

  //Retrofit
  //  implementation 'com.squareup.retrofit2:converter-gson:2.3.0'
  //  implementation 'com.squareup.retrofit2:adapter-rxjava2:2.3.0'

  //view注解
  implementation 'com.jakewharton:butterknife:10.2.3'
  annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

  //图片加载  3.7.0
  implementation 'com.github.bumptech.glide:glide:4.12.0'

  //Log
  implementation 'com.orhanobut:logger:2.2.0'

  //底部切换按钮
  implementation 'com.github.chaychan:BottomBarLayout:1.1.2'

  //SP工具类
  implementation 'com.orhanobut:hawk:2.0.1'

  //解析JSON

  //  //Bugly集成
  //  implementation 'com.tencent.bugly:crashreport:latest.release'
  //  implementation 'com.tencent.bugly:nativecrashreport:latest.release'

  //极光推送
  implementation 'cn.jiguang.sdk:jcore:2.7.2'
  implementation 'cn.jiguang.sdk:jpush:4.0.0'  

  //上拉加载，下拉刷新
  implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.0.5.1'
  implementation 'com.scwang.smartrefresh:SmartRefreshHeader:1.0.5.1'


  //圆角图片
  implementation 'de.hdodenhof:circleimageview:2.2.0'

  //数据库
  implementation 'org.greenrobot:greendao:3.3.0'

  //滚轮效果
  implementation 'com.contrarywind:Android-PickerView:4.1.6'

  //图片预览
  implementation 'com.github.chrisbanes:PhotoView:2.0.0'

  //加密协议
  implementation 'org.bouncycastle:bcprov-jdk16:1.45'
  implementation 'commons-codec:commons-codec:1.10'

  //权限申请
  implementation 'pub.devrel:easypermissions:1.1.1'

  //tab布局
  implementation 'com.flyco.tablayout:FlycoTabLayout_Lib:2.1.2@aar'

  implementation 'com.squareup.okhttp3:okhttp:4.9.1'

  //扫码功能
  implementation 'cn.bingoogolapple:bga-qrcode-zxing:1.3.7'

  //必选，内部默认使用系统mediaplayer进行解码
  implementation 'com.github.dueeeke.dkplayer:dkplayer-java:3.2.6'

  //可选，包含StandardVideoController的实现
  implementation 'com.github.dueeeke.dkplayer:dkplayer-ui:3.2.6'

  //可选，使用exoplayer进行解码
  implementation 'com.github.dueeeke.dkplayer:player-exo:3.2.6'

  // debugImplementation because LeakCanary should only run in debug builds.
  //debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.6'

  implementation 'com.airbnb.android:lottie:3.5.0'

  implementation 'com.amap.api:3dmap:7.8.0'
  implementation 'com.amap.api:search:7.7.0'

  // CameraX core library
  def camerax_version = '1.1.0-alpha11'
  implementation "androidx.camera:camera-core:$camerax_version"
  implementation "androidx.camera:camera-camera2:$camerax_version"
  implementation "androidx.camera:camera-lifecycle:$camerax_version"
  implementation "androidx.camera:camera-video:$camerax_version"

  implementation "androidx.camera:camera-view:1.0.0-alpha30"

  // CameraX Extensions library
  implementation 'androidx.camera:camera-extensions:1.0.0-alpha27'
  // Kotlin lang
  implementation 'androidx.core:core-ktx:1.6.0'
  implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
  implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0'
  implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.1'
  implementation "androidx.concurrent:concurrent-futures-ktx:1.1.0"

  // App compat and UI things
  implementation 'androidx.appcompat:appcompat:1.4.0'
  implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.3.1'
  implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

  implementation 'com.google.android.exoplayer:exoplayer:2.14.1'



}