// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    ext {
        kotlin_version = '1.4.21'
    }
    repositories {
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath 'com.novoda:bintray-release:0.7.0'
        //greenDao
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version" // add plugin
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.tencent.mm:AndResGuard-gradle-plugin:1.2.21'
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url "https://maven.google.com" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
  
}
