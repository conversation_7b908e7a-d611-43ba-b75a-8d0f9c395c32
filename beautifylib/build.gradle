apply plugin: 'com.android.library'

android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 15
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    useLibrary 'org.apache.http.legacy'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    androidTestImplementation('androidx.test.espresso:espresso-core:3.1.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    implementation 'androidx.appcompat:appcompat:1.0.0'
    testImplementation 'junit:junit:4.12'
    implementation 'com.github.bumptech.glide:glide:3.7.0'
    implementation 'com.github.chrisbanes.photoview:library:1.2.4'
    implementation 'com.squareup.picasso:picasso:2.5.2'
    implementation 'pub.devrel:easypermissions:0.3.1'

//    compile 'com.flying.xiaopo:sticker:1.4.0'

    implementation 'org.greenrobot:eventbus:3.0.0'
    //裁剪库 - 更新到支持 16KB 页面大小的版本
//    compile 'com.yalantis:ucrop:2.2.0'
    implementation 'com.yalantis:ucrop:2.2.8'
}
