<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android">


  <style name="camerasdk_tab_text">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:gravity">center</item>
    <item name="android:layout_gravity">center</item>
    <item name="android:textColor">@android:color/black</item>
    <item name="android:textSize">16sp</item>
    <item name="android:clickable">true</item>
    <item name="android:padding">10dp</item>
  </style>


  <style name="camerasdk_radio_button">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">wrap_content</item>
    <item name="android:gravity">center</item>
    <item name="android:layout_gravity">center</item>
    <item name="android:button">@null</item>
    <item name="android:background">@null</item>
    <item name="android:textColor">@android:color/white</item>
    <item name="android:textSize">11sp</item>
    <item name="android:drawablePadding">3dip</item>
  </style>


</resources>