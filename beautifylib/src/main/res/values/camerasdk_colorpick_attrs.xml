<?xml version="1.0" encoding="utf-8"?>
<resources>

  <declare-styleable name="ColorPicker">
    <attr name="color_wheel_radius" format="dimension"/>
    <attr name="color_wheel_thickness" format="dimension"/>
    <attr name="color_center_radius" format="dimension"/>
    <attr name="color_center_halo_radius" format="dimension"/>
    <attr name="color_pointer_radius" format="dimension"/>
    <attr name="color_pointer_halo_radius" format="dimension"/>
  </declare-styleable>
  <declare-styleable name="ColorBars">
    <attr name="bar_thickness" format="dimension"/>
    <attr name="bar_length" format="dimension"/>
    <attr name="bar_pointer_radius" format="dimension"/>
    <attr name="bar_pointer_halo_radius" format="dimension"/>
    <attr name="bar_orientation_horizontal" format="boolean"/>
  </declare-styleable>

</resources>