<?xml version="1.0" encoding="utf-8"?>
<set
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:fillBefore="true"
    android:fillAfter="true">
  <scale
      android:duration="100"
      android:fromXScale="1.0"
      android:fromYScale="1.0"
      android:interpolator="@android:anim/accelerate_decelerate_interpolator"
      android:pivotX="50.0%"
      android:pivotY="50.0%"
      android:toXScale="0.9"
      android:toYScale="0.9"/>
  <scale
      android:duration="100"
      android:fromXScale="0.9"
      android:fromYScale="0.9"
      android:interpolator="@android:anim/accelerate_decelerate_interpolator"
      android:pivotX="50.0%"
      android:pivotY="50.0%"
      android:toXScale="1.1"
      android:toYScale="1.1"
      android:startOffset="100"/>
  <scale
      android:duration="100"
      android:fromXScale="1.1"
      android:fromYScale="1.1"
      android:interpolator="@android:anim/accelerate_decelerate_interpolator"
      android:pivotX="50.0%"
      android:pivotY="50.0%"
      android:toXScale="1.0"
      android:toYScale="1.0"
      android:startOffset="200"/>
  <scale
      android:duration="150"
      android:fromXScale="1.0"
      android:fromYScale="1.0"
      android:interpolator="@android:anim/accelerate_decelerate_interpolator"
      android:pivotX="50.0%"
      android:pivotY="50.0%"
      android:toXScale="1.25"
      android:toYScale="1.25"
      android:startOffset="300"/>
  <scale
      android:duration="100"
      android:fromXScale="1.25"
      android:fromYScale="1.25"
      android:interpolator="@android:anim/accelerate_decelerate_interpolator"
      android:pivotX="50.0%"
      android:pivotY="50.0%"
      android:toXScale="1.0"
      android:toYScale="1.0"
      android:startOffset="350"/>
</set>
