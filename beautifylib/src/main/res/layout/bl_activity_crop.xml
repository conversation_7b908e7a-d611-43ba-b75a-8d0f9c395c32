<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

  <com.yalantis.ucrop.view.UCropView
      android:id="@+id/crop_u_crop_view"
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      >

  </com.yalantis.ucrop.view.UCropView>

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="vertical"
      >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        >
      <TextView
          android:id="@+id/crop_ratio_1_1"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="10dp"
          android:text="1:1"
          android:gravity="center"
          />

      <TextView
          android:id="@+id/crop_ratio_3_4"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="10dp"
          android:text="3:4"
          android:gravity="center"
          />
      <TextView
          android:id="@+id/crop_ratio_original"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="10dp"
          android:text="原始比例"
          android:gravity="center"
          />
      <TextView
          android:id="@+id/crop_ratio_3_2"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="10dp"
          android:text="3:2"
          android:gravity="center"
          />
      <TextView
          android:id="@+id/crop_ratio_16_9"
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:padding="10dp"
          android:text="16:9"
          android:gravity="center"
          />
    </LinearLayout>

    <!--<View-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="1dp"-->
    <!--android:background="@color/camerasdk_divider_color"/>-->

    <!--<LinearLayout-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:orientation="horizontal">-->

    <!--</LinearLayout>-->

  </LinearLayout>

</LinearLayout>