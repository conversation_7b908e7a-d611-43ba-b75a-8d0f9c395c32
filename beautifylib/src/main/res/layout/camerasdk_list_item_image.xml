<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    >

  <!-- <ImageView
      android:id="@+id/image"
      android:scaleType="centerCrop"
      android:src="@drawable/default_error"
      android:layout_width="90dip"
      android:layout_height="90dip" /> -->

  <ImageView
      android:id="@+id/image"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:contentDescription="@null"
      android:layout_centerInParent="true"
      />

  <View
      android:visibility="gone"
      android:id="@+id/mask"
      android:background="#88000000"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      />

  <ImageView
      android:id="@+id/checkmark"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="top|right"
      android:src="@drawable/bl_check_normal"
      />

</FrameLayout>