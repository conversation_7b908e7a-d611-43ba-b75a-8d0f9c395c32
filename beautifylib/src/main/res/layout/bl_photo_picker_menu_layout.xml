<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:padding="10dp"
    >

  <TextView
      android:id="@+id/photo_picker_menu_item_complete"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignParentRight="true"
      android:text="完成(0/9)"
      android:textColor="@android:color/white"
      android:layout_centerVertical="true"
      />

  <TextView
      android:id="@+id/photo_picker_menu_item_title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="所有图片"
      android:textSize="18sp"
      android:textColor="@android:color/white"
      android:layout_centerVertical="true"
      />
  <ImageView
      android:id="@+id/photo_picker_menu_item_arrow"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:src="@drawable/down_arrow"
      android:layout_toRightOf="@+id/photo_picker_menu_item_title"
      android:layout_marginLeft="10dp"
      android:layout_centerVertical="true"
      />
</RelativeLayout>