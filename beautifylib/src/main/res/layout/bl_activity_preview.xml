<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

  <!--<CheckBox-->
  <!--android:id="@+id/preview_check_mark"-->
  <!--android:button="@drawable/camerasdk_selector_checkbox"-->
  <!--android:layout_width="wrap_content"-->
  <!--android:layout_height="wrap_content"-->
  <!--android:layout_gravity="right"-->
  <!--android:checked="false"-->
  <!--android:clickable="false"-->
  <!--android:focusable="false"-->
  <!--android:layout_margin="10dp"/>-->

  <ImageView
      android:id="@+id/preview_check_mark"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:src="@drawable/bl_check_normal"
      android:layout_gravity="right"
      android:layout_margin="10dp"
      />

  <com.yyx.beautifylib.view.CustomViewPager
      android:id="@+id/preview_viewpager"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="center"
      />
</LinearLayout>