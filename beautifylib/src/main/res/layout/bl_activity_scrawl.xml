<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      android:gravity="center"
      android:orientation="horizontal"
      android:background="@android:color/black"
      >

    <com.yyx.beautifylib.scrawl.DrawingBoardView
        android:id="@+id/scrawl_draw_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        />
  </LinearLayout>

  <FrameLayout
      android:layout_width="match_parent"
      android:layout_height="80dp"
      >

    <com.muzhi.camerasdk.library.views.HorizontalListView
        android:id="@+id/scrawl_paint_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        >

    </com.muzhi.camerasdk.library.views.HorizontalListView>


    <SeekBar
        android:id="@+id/scrawl_paint_size_sb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@android:color/transparent"
        android:max="10"
        android:progress="5"
        android:thumb="@drawable/camerasdk_icon_seek_thumb"
        android:visibility="gone"
        />
  </FrameLayout>

  <View
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:background="@android:color/darker_gray"
      android:layout_marginBottom="5dp"
      android:layout_marginTop="5dp"
      />

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      >

    <TextView
        android:id="@+id/scrawl_paint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="画笔"
        android:textColor="@android:color/black"
        />

    <TextView
        android:id="@+id/scrawl_paint_size"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="大小"
        android:textColor="@android:color/black"
        />

    <TextView
        android:id="@+id/scrawl_paint_color"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="颜色"
        android:textColor="@android:color/black"
        />
  </LinearLayout>
</LinearLayout>