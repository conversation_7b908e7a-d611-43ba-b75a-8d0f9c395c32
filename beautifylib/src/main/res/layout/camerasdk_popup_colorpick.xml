<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:clipToPadding="true"
    android:gravity="center"
    android:background="@android:color/transparent"
    >


  <LinearLayout
      android:id="@+id/ll_popup"
      android:layout_width="200dip"
      android:layout_height="250dip"
      android:orientation="vertical"
      android:gravity="center"
      >

    <com.yyx.beautifylib.view.holocolorpicker.ColorPicker
        android:id="@+id/picker"
        android:layout_width="180dip"
        android:layout_height="180dip"
        />

    <com.yyx.beautifylib.view.holocolorpicker.SVBar
        android:id="@+id/svbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        />

    <com.yyx.beautifylib.view.holocolorpicker.OpacityBar
        android:id="@+id/opacitybar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        />

  </LinearLayout>


</RelativeLayout>

