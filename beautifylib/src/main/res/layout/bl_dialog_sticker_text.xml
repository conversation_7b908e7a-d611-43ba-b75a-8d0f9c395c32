<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:padding="10dp"
    >
  <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="文字贴图"
      android:textSize="20sp"
      android:textColor="@android:color/black"
      />
  <View
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:background="@android:color/darker_gray"
      android:layout_marginTop="10dp"
      android:layout_marginBottom="10dp"
      />
  <EditText
      android:id="@+id/dialog_sticker_text"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:singleLine="true"
      android:hint="请输入文字"
      />
  <Button
      android:id="@+id/dialog_sticker_text_confirm_btn"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:text="确定"
      android:textColor="@android:color/white"
      android:layout_marginTop="20dp"
      android:padding="0dp"
      />
</LinearLayout>