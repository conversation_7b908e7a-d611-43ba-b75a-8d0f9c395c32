<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    >

  <EditText
      android:id="@+id/tag01"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:hint="输入标签一"
      android:textColorHint="#c6c6c6"
      />

  <EditText
      android:id="@+id/tag02"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="5dp"
      android:hint="输入标签二"
      android:textColorHint="#c6c6c6"
      />

  <EditText
      android:id="@+id/tag03"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="5dp"
      android:hint="输入标签三"
      android:textColorHint="#c6c6c6"
      />

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="10dp"
      android:orientation="horizontal"
      >

    <Button
        android:id="@+id/cancelBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="取消"
        />

    <Space
        android:layout_width="10dp"
        android:layout_height="wrap_content"
        />

    <Button
        android:id="@+id/confirmBtn"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="创建"
        />
  </LinearLayout>

</LinearLayout>