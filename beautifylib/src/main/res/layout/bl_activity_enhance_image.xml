<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >

  <FrameLayout
      android:layout_width="match_parent"
      android:layout_height="0dp"
      android:layout_weight="1"
      android:layout_marginBottom="20dp"
      android:background="@android:color/black"
      >

    <com.muzhi.camerasdk.library.filter.GPUImageView
        android:id="@+id/enhance_image"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        />
  </FrameLayout>


  <SeekBar
      android:id="@+id/enhance_seekbar"
      android:layout_width="fill_parent"
      android:layout_height="wrap_content"
      android:background="@android:color/transparent"
      android:max="255"
      android:progress="127"
      android:thumb="@drawable/camerasdk_icon_seek_thumb"
      />

  <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:orientation="horizontal"
      >

    <TextView
        android:id="@+id/enhance_tab_brightness"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="亮度"
        android:textColor="@android:color/black"
        />

    <TextView
        android:id="@+id/enhance_tab_contrast"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="对比度"
        android:textColor="@android:color/black"
        />

    <TextView
        android:id="@+id/enhance_tab_saturation"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:padding="10dp"
        android:text="饱和度"
        android:textColor="@android:color/black"
        />
  </LinearLayout>

</LinearLayout>