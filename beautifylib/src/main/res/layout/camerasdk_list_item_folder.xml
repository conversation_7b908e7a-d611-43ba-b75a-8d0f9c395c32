<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="10dp"
    android:paddingBottom="10dp"
    android:background="@android:color/white"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

  <ImageView
      android:layout_centerVertical="true"
      android:layout_marginLeft="10dp"
      android:id="@+id/cover"
      tools:src="@drawable/btn_back"
      android:layout_gravity="center_vertical"
      android:scaleType="centerInside"
      android:src="@drawable/camerasdk_pic_loading"
      android:layout_width="@dimen/folder_cover_size"
      android:layout_height="@dimen/folder_cover_size"
      />

  <LinearLayout
      android:layout_toRightOf="@+id/cover"
      android:layout_toLeftOf="@+id/indicator"
      android:layout_centerVertical="true"
      android:layout_marginLeft="16dp"
      android:orientation="vertical"
      android:layout_gravity="center_vertical"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      >

    <TextView
        android:id="@+id/name"
        tools:text="img"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

    <TextView
        android:singleLine="true"
        android:ellipsize="end"
        android:id="@+id/size"
        tools:text="1张"
        android:layout_marginTop="5dp"
        android:textSize="12sp"
        android:textColor="#AFAFAF"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        />

  </LinearLayout>

  <ImageView
      android:layout_alignParentRight="true"
      android:layout_centerVertical="true"
      android:id="@+id/indicator"
      android:layout_marginLeft="10dp"
      android:layout_marginRight="20dp"
      android:src="@drawable/default_check"
      android:layout_gravity="center_vertical"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      />

</RelativeLayout>