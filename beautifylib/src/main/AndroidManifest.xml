<manifest xmlns:android="http://schemas.android.com/apk/res/android"

    package="com.yyx.beautifylib">

  <application
      android:allowBackup="true"
      android:label="@string/app_name"
      android:supportsRtl="true">
    <activity android:name=".ui.activity.BLPhotoPickActivity"/>
    <activity android:name=".ui.activity.BLPreviewActivity"/>
    <activity android:name=".ui.activity.BLBeautifyImageActivity"/>
    <activity android:name=".ui.activity.BLCropActivity"/>
    <activity android:name=".ui.activity.BLEnhanceImageActivity"/>
    <activity android:name=".ui.activity.BLScrawlActivity"/>
    <activity
        android:name="com.yalantis.ucrop.UCropActivity"
        android:screenOrientation="portrait"
        android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
  </application>

  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.CAMERA"/>

</manifest>
