package com.yyx.beautifylib.utils;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

/**
 * Created by Administrator on 2017/4/16.
 */

public class BLCommonUtils {
  public BLCommonUtils() {
  }

  public static String getApplicationName(Context mContext) {
    PackageManager packageManager = null;
    ApplicationInfo applicationInfo = null;

    try {
      packageManager = mContext.getApplicationContext().getPackageManager();
      applicationInfo = packageManager.getApplicationInfo(mContext.getPackageName(), 0);
    } catch (PackageManager.NameNotFoundException var4) {
      applicationInfo = null;
    }

    String applicationName = (String) packageManager.getApplicationLabel(applicationInfo);
    return applicationName;
  }
}
