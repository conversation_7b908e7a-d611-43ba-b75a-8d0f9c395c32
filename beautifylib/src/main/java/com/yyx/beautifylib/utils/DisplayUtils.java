/**
 * Copyright 2014 <PERSON><PERSON> (<EMAIL>)
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yyx.beautifylib.utils;

import android.app.Activity;
import android.content.Context;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

/**
 * 系统显示相关工具类
 *
 * <AUTHOR>
 */
public final class DisplayUtils {

  /**
   * Don't let anyone instantiate this class.
   */
  private DisplayUtils() {
    throw new Error("Do not need instantiate!");
  }

  /**
   * 根据手机的分辨率从 dp 的单位 转成为 px(像素)
   *
   * @param context 上下文
   * @param dpValue 尺寸dip
   * @return 像素值
   */
  public static int dip2px(Context context, float dpValue) {
    final float scale = context.getResources().getDisplayMetrics().density;
    return (int) (dpValue * scale + 0.5f);
  }

  /**
   * 根据手机的分辨率从 px(像素) 的单位 转成为 dp
   *
   * @param context 上下文
   * @param pxValue 尺寸像素
   * @return DIP值
   */
  public static int px2dip(Context context, float pxValue) {
    final float scale = context.getResources().getDisplayMetrics().density;
    return (int) (pxValue / scale + 0.5f);
  }

  /**
   * 根据手机的分辨率从 px(像素) 的单位 转成为 sp
   *
   * @param context 上下文
   * @param pxValue 尺寸像素
   * @return SP值
   */
  public static int px2sp(Context context, float pxValue) {
    float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (pxValue / fontScale + 0.5f);
  }

  /**
   * 根据手机的分辨率从 sp 的单位 转成为 px
   *
   * @param context 上下文
   * @param spValue SP值
   * @return 像素值
   */
  public static int sp2px(Context context, float spValue) {
    float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (spValue * fontScale + 0.5f);
  }

  /**
   * 获取dialog宽度
   *
   * @param activity Activity
   * @return Dialog宽度
   */
  public static int getDialogW(Activity activity) {
    DisplayMetrics dm = new DisplayMetrics();
    dm = activity.getResources().getDisplayMetrics();
    int w = dm.widthPixels - 100;
    // int w = aty.getWindowManager().getDefaultDisplay().getWidth() - 100;
    return w;
  }

  /**
   * 获取屏幕宽度
   *
   * @param activity Activity
   * @return 屏幕宽度
   */
  public static int getScreenW(Activity activity) {
    DisplayMetrics dm = new DisplayMetrics();
    dm = activity.getResources().getDisplayMetrics();
    int w = dm.widthPixels;
    // int w = aty.getWindowManager().getDefaultDisplay().getWidth();
    return w;
  }

  /**
   * 获取屏幕高度
   *
   * @param activity Activity
   * @return 屏幕高度
   */
  public static int getScreenH(Activity activity) {
    DisplayMetrics dm = new DisplayMetrics();
    dm = activity.getResources().getDisplayMetrics();
    int h = dm.heightPixels;
    // int h = aty.getWindowManager().getDefaultDisplay().getHeight();
    return h;
  }

  /**
   * Toggle keyboard If the keyboard is visible,then hidden it,if it's
   * invisible,then show it
   *
   * @param context 上下文
   */
  public static void toggleKeyboard(Context context) {
    InputMethodManager imm = (InputMethodManager) context
        .getSystemService(Context.INPUT_METHOD_SERVICE);
    if (imm.isActive()) {
      imm.toggleSoftInput(InputMethodManager.SHOW_IMPLICIT,
          InputMethodManager.HIDE_NOT_ALWAYS);
    }
  }

  /**
   * 隐藏键盘
   */
  public static void hideKeyboard(Context context, View view) {
    InputMethodManager imm = (InputMethodManager) context
        .getSystemService(Context.INPUT_METHOD_SERVICE);
    if (imm.isActive()) {
      imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }
  }

  /**
   * 判断键盘是否显示
   */
  public static boolean isKeyboradShow(Context context) {
    InputMethodManager imm = (InputMethodManager) context
        .getSystemService(Context.INPUT_METHOD_SERVICE);
    return imm.isActive();
  }
}
